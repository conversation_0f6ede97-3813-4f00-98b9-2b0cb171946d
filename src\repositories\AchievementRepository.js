/**
 * Achievement Repository
 * Following Single Responsibility Principle - handles achievement data persistence
 */

class AchievementRepository extends BaseRepository {
  constructor() {
    const headers = ["Usuario", "LogroID", "Fecha", "Puntos", "Categoria", "Notificado"];
    super(AppConfig.SHEET_NAMES.USER_ACHIEVEMENTS, headers);
  }

  /**
   * Get achievements for a user
   * @param {string} userName - User name
   * @returns {Array}
   */
  getUserAchievements(userName) {
    const rows = this.getWhere(row => row[0] === userName);
    return rows.map(row => Achievement.userAchievementFromSheetRow(row));
  }

  /**
   * Check if user has specific achievement
   * @param {string} userName - User name
   * @param {string} achievementId - Achievement ID
   * @returns {boolean}
   */
  hasUserAchievement(userName, achievementId) {
    const achievement = this.getFirst(row => row[0] === userName && row[1] === achievementId);
    return achievement !== null;
  }

  /**
   * Grant achievement to user
   * @param {string} userName - User name
   * @param {Achievement} achievement - Achievement object
   * @param {Date} dateEarned - Date earned
   * @returns {boolean}
   */
  grantAchievement(userName, achievement, dateEarned = new Date()) {
    if (this.hasUserAchievement(userName, achievement.id)) {
      return false; // Already has achievement
    }

    const rowData = achievement.toSheetRow(userName, dateEarned, false);
    return this.create(rowData);
  }

  /**
   * Get pending notifications for user
   * @param {string} userName - User name
   * @returns {Array}
   */
  getPendingNotifications(userName) {
    const rows = this.getWhere(row => row[0] === userName && !row[5]); // Not notified
    return rows.map(row => Achievement.userAchievementFromSheetRow(row));
  }

  /**
   * Mark achievements as notified
   * @param {string} userName - User name
   * @param {Array} achievementIds - Array of achievement IDs
   * @returns {boolean}
   */
  markAsNotified(userName, achievementIds) {
    try {
      const sheet = this.getSheet();
      const allData = this.getAll();
      
      for (let i = 0; i < allData.length; i++) {
        const row = allData[i];
        if (row[0] === userName && achievementIds.includes(row[1])) {
          // Mark as notified (column 6, index 5)
          sheet.getRange(i + 2, 6).setValue(true);
        }
      }
      return true;
    } catch (error) {
      console.log(`❌ Error marking achievements as notified: ${error.message}`);
      return false;
    }
  }

  /**
   * Get achievement statistics
   * @returns {Object}
   */
  getAchievementStats() {
    const allAchievements = this.getAll();
    const stats = {};
    
    allAchievements.forEach(row => {
      const achievementId = row[1];
      const category = row[4];
      
      if (!stats[achievementId]) {
        stats[achievementId] = {
          count: 0,
          category: category,
          users: []
        };
      }
      
      stats[achievementId].count++;
      stats[achievementId].users.push(row[0]);
    });
    
    return stats;
  }

  /**
   * Get rarest achievements (least earned)
   * @param {number} limit - Number of achievements to return
   * @returns {Array}
   */
  getRarestAchievements(limit = 5) {
    const stats = this.getAchievementStats();
    const achievements = Object.entries(stats)
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => a.count - b.count)
      .slice(0, limit);
    
    return achievements;
  }

  /**
   * Get most popular achievements
   * @param {number} limit - Number of achievements to return
   * @returns {Array}
   */
  getMostPopularAchievements(limit = 5) {
    const stats = this.getAchievementStats();
    const achievements = Object.entries(stats)
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
    
    return achievements;
  }

  /**
   * Get achievements by category
   * @param {string} category - Achievement category
   * @returns {Array}
   */
  getAchievementsByCategory(category) {
    const rows = this.getWhere(row => row[4] === category);
    return rows.map(row => Achievement.userAchievementFromSheetRow(row));
  }

  // Implementation of IRepository interface methods
  getById(id) {
    // For achievements, we'll use userName as ID
    return this.getUserAchievements(id);
  }

  update(id, data) {
    // Achievements are typically not updated, only granted
    return false;
  }

  delete(id) {
    // For safety, we don't allow deleting achievements
    return false;
  }
}
