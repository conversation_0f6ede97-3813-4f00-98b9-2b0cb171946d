/**
 * Game Configuration Repository
 * Following Single Responsibility Principle - handles game configuration persistence
 */

class GameConfigRepository extends BaseRepository {
  constructor() {
    const headers = ["<PERSON><PERSON>", "<PERSON>or", "Activo", "Descripción"];
    super(AppConfig.SHEET_NAMES.GAME_CONFIG, headers);
  }

  /**
   * Get current game configuration
   * @returns {GameConfig}
   */
  getGameConfig() {
    const data = this.getAll();
    
    if (data.length === 0) {
      // Initialize with default configuration
      this.initializeDefaultConfig();
      return GameConfig.getDefault();
    }
    
    return GameConfig.fromSheetData([this.headers, ...data]);
  }

  /**
   * Initialize default configuration
   * @returns {boolean}
   */
  initializeDefaultConfig() {
    try {
      const defaultConfig = AppConfig.DEFAULT_GAME_CONFIG;
      
      // Clear existing content
      this.clearContent();
      
      // Add default configuration
      defaultConfig.forEach(configRow => {
        this.create(configRow);
      });
      
      return true;
    } catch (error) {
      console.log(`❌ Error initializing default config: ${error.message}`);
      return false;
    }
  }

  /**
   * Update configuration rule
   * @param {string} ruleName - Rule name
   * @param {number} value - New value
   * @param {boolean} active - Whether rule is active
   * @returns {boolean}
   */
  updateRule(ruleName, value, active = true) {
    const rowIndex = this.findRowIndex(row => row[0] === ruleName);
    
    if (rowIndex > 0) {
      const sheet = this.getSheet();
      sheet.getRange(rowIndex, 2, 1, 2).setValues([[value, active]]);
      return true;
    } else {
      // Create new rule
      return this.create([ruleName, value, active, `Custom rule: ${ruleName}`]);
    }
  }

  /**
   * Toggle rule active status
   * @param {string} ruleName - Rule name
   * @returns {boolean}
   */
  toggleRule(ruleName) {
    const rowIndex = this.findRowIndex(row => row[0] === ruleName);
    
    if (rowIndex > 0) {
      const sheet = this.getSheet();
      const currentActive = sheet.getRange(rowIndex, 3).getValue();
      sheet.getRange(rowIndex, 3).setValue(!currentActive);
      return true;
    }
    
    return false;
  }

  /**
   * Get active rules only
   * @returns {Array}
   */
  getActiveRules() {
    return this.getWhere(row => row[2] === true);
  }

  /**
   * Get rule by name
   * @param {string} ruleName - Rule name
   * @returns {Array|null}
   */
  getRule(ruleName) {
    return this.getFirst(row => row[0] === ruleName);
  }

  /**
   * Backup current configuration
   * @returns {Array}
   */
  backupConfiguration() {
    return this.getAll();
  }

  /**
   * Restore configuration from backup
   * @param {Array} backupData - Backup data
   * @returns {boolean}
   */
  restoreConfiguration(backupData) {
    try {
      this.clearContent();
      
      backupData.forEach(row => {
        this.create(row);
      });
      
      return true;
    } catch (error) {
      console.log(`❌ Error restoring configuration: ${error.message}`);
      return false;
    }
  }

  /**
   * Reset to default configuration
   * @returns {boolean}
   */
  resetToDefault() {
    return this.initializeDefaultConfig();
  }

  // Implementation of IRepository interface methods
  getById(id) {
    return this.getRule(id);
  }

  update(id, data) {
    if (Array.isArray(data) && data.length >= 3) {
      return this.updateRule(id, data[1], data[2]);
    }
    return false;
  }

  delete(id) {
    const rowIndex = this.findRowIndex(row => row[0] === id);
    return rowIndex > 0 ? this.deleteByIndex(rowIndex) : false;
  }
}
