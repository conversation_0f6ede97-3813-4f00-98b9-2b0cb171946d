// === GAMIFICATION SYSTEM v2 — FUNCIONALIDADES COMPLETAS (Discord + Lucky Day + Word of Day + Rebuild) ===
// Sustituye TODO el archivo por este contenido.
// ---------------------------------------------------------
//
// 🎮 NUEVAS CARACTERÍSTICAS v3:
// ✅ Participantes dinámicos (auto-detección desde formularios)
// ✅ Sistema de logros/achievements configurables
// ✅ Configuración flexible de puntos y bonificaciones
// ✅ Ranking semanal automático
// ✅ Herramientas de mantenimiento y debugging
// ✅ Triggers automáticos para tareas programadas
// ✅ Documentación integrada en hoja de ayuda
//
// 🏆 LOGROS IMPLEMENTADOS:
// 🌅 Madrugador: Daily antes de 9:00 AM (+2 pts)
// ⚔️ Guerrero de Fin de Semana: Daily en sábado/domingo (+3 pts)
// 📝 Detallista: Daily con +200 caracteres (+1 pt)
//
// ⚙️ CONFIGURACIÓN FLEXIBLE:
// - Hoja "GameConfig": Configura puntos, bonificaciones y multiplicadores
// - Participantes auto-detectados desde respuestas del formulario
// - Sistema extensible para agregar nuevos logros fácilmente
//
// 🚀 PARA EMPEZAR:
// 1. Ejecutar setupAutomaticTriggers() una vez para automatizar
// 2. Ejecutar createHelpSheet() para generar documentación
// 3. Revisar hoja "GameConfig" para ajustar configuración
// ---------------------------------------------------------

/* ------------------ CONFIGURACIÓN GENERAL ------------------ */
const DISCORD_WEBHOOK_URL = "https://discord.com/api/webhooks/1387858289873916025/i4FRnGq-SgdzNL7e7vNtbx4ouJZfH53ls_-ZWxMsGKRnYrN7LHN0xj5VbXiyAdIS9aO4";

const GAMIF_SHEET_NAME  = "Gamification";      // hoja destino
const SOURCE_SHEET_NAME = "NDailies";           // hoja con dptos iniciales
const CONFIG_SHEET_NAME = "Config";            // LuckyDay / WordOfDay
const NAME_FIELD = "IDENTIFICATE🤬";            // nombre en formulario

// PARTICIPANTES ahora se detectan dinámicamente
// const PARTICIPANTES = ["Ian","Rubén","Laura","Camilo","Juan Sebastián","Gerard","Dani","Paola","Grazielly","Martí","Marc","Alex Salueña","Eduard","Guillem","Aran","Alex Ávila"];

const BADGE_MILESTONES = [
  { days: 3, emoji: "🔥" },
  { days: 5, emoji: "🏅" },
  { days: 7, emoji: "🌟" },
  { days: 14, emoji: "🚀" },
  { days: 30, emoji: "🏆" }
];

// Sistema completo de logros únicos (solo se obtienen una vez)
const UNIQUE_ACHIEVEMENTS = [
  // === LOGROS INICIALES ===
  {
    id: "first_daily",
    name: "Primer Paso",
    emoji: "🎯",
    points: 5,
    description: "Completar tu primera daily",
    category: "Inicio",
    condition: (_, userStats) => userStats.totalDailies === 1
  },

  // === LOGROS POR VOLUMEN ===
  {
    id: "sharpshooter",
    name: "Francotirador",
    emoji: "🎯",
    points: 10,
    description: "Completar 10 dailies",
    category: "Volumen",
    condition: (_, userStats) => userStats.totalDailies === 10
  },
  {
    id: "expert_archer",
    name: "Arquero Experto",
    emoji: "🏹",
    points: 20,
    description: "Completar 25 dailies",
    category: "Volumen",
    condition: (_, userStats) => userStats.totalDailies === 25
  },
  {
    id: "veteran",
    name: "Veterano",
    emoji: "🎖️",
    points: 35,
    description: "Completar 50 dailies",
    category: "Volumen",
    condition: (_, userStats) => userStats.totalDailies === 50
  },
  {
    id: "legend",
    name: "Leyenda",
    emoji: "🏆",
    points: 50,
    description: "Completar 100 dailies",
    category: "Volumen",
    condition: (_, userStats) => userStats.totalDailies === 100
  },

  // === LOGROS POR RACHAS ===
  {
    id: "on_fire",
    name: "En Llamas",
    emoji: "🔥",
    points: 15,
    description: "Racha de 5 días consecutivos",
    category: "Racha",
    condition: (_, userStats) => userStats.currentStreak === 5
  },
  {
    id: "supernova",
    name: "Supernova",
    emoji: "�",
    points: 25,
    description: "Racha de 10 días consecutivos",
    category: "Racha",
    condition: (_, userStats) => userStats.currentStreak === 10
  },
  {
    id: "unstoppable",
    name: "Imparable",
    emoji: "🚀",
    points: 40,
    description: "Racha de 15 días consecutivos",
    category: "Racha",
    condition: (_, userStats) => userStats.currentStreak === 15
  },
  {
    id: "consistency_king",
    name: "Rey de la Consistencia",
    emoji: "👑",
    points: 75,
    description: "Racha de 30 días consecutivos",
    category: "Racha",
    condition: (_, userStats) => userStats.currentStreak === 30
  },

  // === LOGROS POR HORARIOS ===
  {
    id: "early_bird",
    name: "Madrugador",
    emoji: "🌅",
    points: 10,
    description: "Completar daily antes de las 9:00 AM",
    category: "Horario",
    condition: (data) => data.timestamp.getHours() < 9
  },
  {
    id: "night_owl",
    name: "Búho Nocturno",
    emoji: "🦉",
    points: 10,
    description: "Completar daily después de las 22:00 PM",
    category: "Horario",
    condition: (data) => data.timestamp.getHours() >= 22
  },
  {
    id: "weekend_warrior",
    name: "Guerrero de Fin de Semana",
    emoji: "⚔️",
    points: 15,
    description: "Completar daily en fin de semana",
    category: "Horario",
    condition: (data) => {
      const day = data.timestamp.getDay();
      return day === 0 || day === 6;
    }
  },

  // === LOGROS POR CONTENIDO ===
  {
    id: "writer",
    name: "Escritor",
    emoji: "📚",
    points: 10,
    description: "Daily con más de 300 caracteres",
    category: "Contenido",
    condition: (data) => (data.ayer + " " + data.hoy + " " + data.imped).length > 300
  },
  {
    id: "creative",
    name: "Creativo",
    emoji: "🎨",
    points: 5,
    description: "Usar emojis en tu daily",
    category: "Contenido",
    condition: (data) => {
      const text = data.ayer + " " + data.hoy + " " + data.imped;
      return /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(text);
    }
  },
  {
    id: "collaborative",
    name: "Colaborativo",
    emoji: "🔗",
    points: 8,
    description: "Mencionar a compañeros en tu daily",
    category: "Contenido",
    condition: (data) => {
      const text = data.ayer + " " + data.hoy + " " + data.imped;

      // 1. Buscar patrones como @nombre
      if (/@\w+/.test(text)) return true;

      // 2. Buscar palabras colaborativas genéricas
      if (/\b(con|junto|equipo|compañer|colabor|ayud|revis|coordin)\b/i.test(text)) return true;

      // 3. Buscar nombres específicos de participantes
      const participants = getAllParticipants();
      const currentUser = data.nombre;

      for (const participant of participants) {
        if (participant !== currentUser) { // No contar menciones a sí mismo
          // Buscar el nombre completo o solo el primer nombre
          const firstName = participant.split(' ')[0];
          const namePattern = new RegExp(`\\b${participant}\\b|\\b${firstName}\\b`, 'i');
          if (namePattern.test(text)) {
            return true;
          }
        }
      }

      return false;
    }
  },
  {
    id: "innovator",
    name: "Innovador",
    emoji: "�",
    points: 8,
    description: "Usar términos técnicos en tu daily",
    category: "Contenido",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      const techTerms = ['api', 'database', 'frontend', 'backend', 'deploy', 'git', 'commit', 'merge', 'pull request', 'bug', 'feature', 'refactor', 'testing', 'docker', 'kubernetes', 'microservice', 'framework', 'library', 'algorithm', 'optimization'];
      return techTerms.some(term => text.includes(term));
    }
  },

  // === LOGROS ESPECIALES/RAROS ===
  {
    id: "lucky_charm",
    name: "Amuleto de la Suerte",
    emoji: "🍀",
    points: 20,
    description: "Completar daily en 3 Lucky Days diferentes",
    category: "Especial",
    condition: (_, userStats) => {
      // Este se evaluará de forma especial en el sistema
      return userStats.luckyDaysHit >= 3;
    }
  },
  {
    id: "chameleon",
    name: "Camaleón",
    emoji: "🎭",
    points: 25,
    description: "Completar dailies en todos los días de la semana",
    category: "Especial",
    condition: (_, userStats) => {
      return userStats.daysOfWeekCompleted >= 7;
    }
  },
  {
    id: "speedster",
    name: "Velocista",
    emoji: "🏃",
    points: 30,
    description: "7 dailies en 7 días diferentes de una semana",
    category: "Especial",
    condition: (_, userStats) => {
      return userStats.perfectWeeks >= 1;
    }
  },
  {
    id: "showman",
    name: "Showman",
    emoji: "🎪",
    points: 15,
    description: "Daily más largo del mes",
    category: "Especial",
    condition: (_, userStats) => {
      return userStats.monthlyLongestDaily === true;
    }
  },
  {
    id: "word_master",
    name: "Maestro de Palabras",
    emoji: "📖",
    points: 20,
    description: "Usar la palabra del día 5 veces",
    category: "Especial",
    condition: (_, userStats) => {
      return userStats.wordOfDayUsed >= 5;
    }
  },
  {
    id: "punctual",
    name: "Puntual",
    emoji: "⏰",
    points: 15,
    description: "Completar daily a la misma hora 5 días seguidos",
    category: "Especial",
    condition: (_, userStats) => {
      return userStats.sameHourStreak >= 5;
    }
  },
  {
    id: "puchaina",
    name: "Potaxie",
    emoji: "🥑",
    points: 11,
    description: "Eres una puchaina 🥑👄🥑",
    category: "Especial",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      const pTerms = ['puchaina','gog','degue','pete','gogog','potaxie','fife','puchaina','jiafei','tilin','tilinx','gog','gogog','pusei','amorch','y la queso','y punch','npc','yassificar','woke','delulu','zutrin','pelgan','glibex', 'y la que'];
      return pTerms.some(term => text.includes(term));
    }
  },

  // === LOGROS POR DEPARTAMENTO ===
  {
    id: "department_ambassador",
    name: "Embajador del Departamento",
    emoji: "🤝",
    points: 25,
    description: "Ser el más activo de tu departamento",
    category: "Departamento",
    condition: (_, userStats) => {
      return userStats.departmentRank === 1;
    }
  },
  {
    id: "team_leader",
    name: "Líder de Equipo",
    emoji: "👥",
    points: 20,
    description: "Superar el promedio del departamento por 20 dailies",
    category: "Departamento",
    condition: (_, userStats) => {
      return userStats.totalDailies > (userStats.departmentAverage + 20);
    }
  },

  // === LOGROS DEL PROYECTO (MOTIVACIÓN ESPECÍFICA) ===
  {
    id: "project_warrior",
    name: "Guerrero del Proyecto",
    emoji: "⚔️",
    points: 15,
    description: "Mencionar trabajo específico del proyecto en daily",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(proyecto|feature|desarrollo|implementar|implementé|código|programar|programé|build|crear|creé)\b/i.test(text);
    }
  },
  {
    id: "problem_solver",
    name: "Solucionador",
    emoji: "🔧",
    points: 20,
    description: "Reportar solución de problemas/bugs",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(solucion|arregl|fix|resolv|complet|termin|finaliz|acabé|acabar|reparé|reparar)\b/i.test(text);
    }
  },
  {
    id: "bug_hunter",
    name: "Cazador de Bugs",
    emoji: "🐛",
    points: 18,
    description: "Encontrar y reportar bugs o problemas",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(bug|error|problema|fallo|issue|defecto|encontré|detecté|identifiqué)\b/i.test(text);
    }
  },
  {
    id: "builder",
    name: "Constructor",
    emoji: "🏗️",
    points: 16,
    description: "Construir o desarrollar nuevas funcionalidades",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(construir|construí|desarrollar|desarrollé|funcionalidad|component|módulo|clase|función)\b/i.test(text);
    }
  },
  {
    id: "tester",
    name: "Probador Experto",
    emoji: "🧪",
    points: 14,
    description: "Realizar testing o pruebas del proyecto",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(test|testing|prueba|probar|probé|verificar|verificué|validar|validé)\b/i.test(text);
    }
  },
  {
    id: "documenter",
    name: "Documentador",
    emoji: "📝",
    points: 12,
    description: "Crear o actualizar documentación",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(documentar|documenté|documentación|readme|wiki|comentar|comenté|explicar|expliqué)\b/i.test(text);
    }
  },
  {
    id: "deployer",
    name: "Lanzador",
    emoji: "🚀",
    points: 25,
    description: "Realizar deploy o lanzamiento de funcionalidades",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(deploy|desplegar|desplegué|lanzar|lancé|publicar|publiqué|release|subir|subí)\b/i.test(text);
    }
  },
  {
    id: "reviewer",
    name: "Revisor de Código",
    emoji: "👀",
    points: 13,
    description: "Revisar código de compañeros",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(revisar|revisé|review|pull request|pr|código|merge|aprobar|aprobé)\b/i.test(text);
    }
  },
  {
    id: "optimizer",
    name: "Optimizador",
    emoji: "⚡",
    points: 17,
    description: "Optimizar rendimiento o mejorar código existente",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(optimizar|optimicé|mejorar|mejoré|refactor|refactorizar|performance|rendimiento)\b/i.test(text);
    }
  },
  {
    id: "planner",
    name: "Planificador",
    emoji: "📋",
    points: 11,
    description: "Planificar tareas o arquitectura del proyecto",
    category: "Proyecto",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return /\b(planificar|planifiqué|diseñar|diseñé|arquitectura|estructura|organizar|organicé)\b/i.test(text);
    }
  },

  // === LOGROS TEMPORALES/EVENTOS ===
  {
    id: "new_year_starter",
    name: "Año Nuevo, Vida Nueva",
    emoji: "🎊",
    points: 10,
    description: "Completar daily el 1 de enero",
    category: "Evento",
    condition: (data) => {
      return data.timestamp.getMonth() === 0 && data.timestamp.getDate() === 1;
    }
  },
  {
    id: "christmas_spirit",
    name: "Espíritu Navideño",
    emoji: "🎄",
    points: 10,
    description: "Completar daily el 25 de diciembre",
    category: "Evento",
    condition: (data) => {
      return data.timestamp.getMonth() === 11 && data.timestamp.getDate() === 25;
    }
  },
  {
    id: "friday_warrior",
    name: "Guerrero del Viernes",
    emoji: "🍻",
    points: 8,
    description: "Completar daily un viernes después de las 18:00",
    category: "Evento",
    condition: (data) => {
      return data.timestamp.getDay() === 5 && data.timestamp.getHours() >= 18;
    }
  },

  // === LOGROS SÚPER RAROS/LEGENDARIOS ===
  {
    id: "midnight_warrior",
    name: "Guerrero de Medianoche",
    emoji: "🌙",
    points: 100,
    description: "Completar daily exactamente a las 00:00:00",
    category: "Legendario",
    condition: (data) => {
      return data.timestamp.getHours() === 0 &&
             data.timestamp.getMinutes() === 0 &&
             data.timestamp.getSeconds() === 0;
    }
  },
  {
    id: "eclipse_master",
    name: "Maestro del Eclipse",
    emoji: "🌘",
    points: 150,
    description: "Completar daily durante un eclipse solar (fechas específicas)",
    category: "Legendario",
    condition: (data) => {
      // Fechas de eclipses solares conocidos
      const eclipseDates = [
        "2025-03-29", "2025-09-21", "2026-02-17", "2026-08-12"
      ];
      const dateStr = data.timestamp.toISOString().split('T')[0];
      return eclipseDates.includes(dateStr);
    }
  },
  {
    id: "palindrome_prophet",
    name: "Profeta del Palíndromo",
    emoji: "🔮",
    points: 75,
    description: "Completar daily en una fecha palíndromo (ej: 12/02/2021)",
    category: "Legendario",
    condition: (data) => {
      const dateStr = Utilities.formatDate(data.timestamp, Session.getScriptTimeZone(), "dd/MM/yyyy");
      const cleanDate = dateStr.replace(/\//g, "");
      return cleanDate === cleanDate.split("").reverse().join("");
    }
  },
  {
    id: "pi_day_genius",
    name: "Genio del Día Pi",
    emoji: "🥧",
    points: 50,
    description: "Completar daily el 14 de marzo a las 15:92 (imposible, pero si alguien lo hace...)",
    category: "Legendario",
    condition: (data) => {
      return data.timestamp.getMonth() === 2 &&
             data.timestamp.getDate() === 14 &&
             data.timestamp.getHours() === 15 &&
             data.timestamp.getMinutes() === 92; // Imposible, pero épico
    }
  },
  {
    id: "fibonacci_master",
    name: "Maestro de Fibonacci",
    emoji: "🌀",
    points: 89,
    description: "Completar daily cuando tu total de dailies sea un número de Fibonacci ≥21",
    category: "Legendario",
    condition: (_, userStats) => {
      const fibNumbers = [21, 34, 55, 89, 144, 233, 377, 610, 987]; // Solo números altos
      return fibNumbers.includes(userStats.totalDailies);
    }
  },
  {
    id: "prime_perfectionist",
    name: "Perfeccionista Primo",
    emoji: "🔢",
    points: 67,
    description: "Completar daily cuando tu total de dailies sea un número primo ≥67",
    category: "Legendario",
    condition: (_, userStats) => {
      const isPrime = (n) => {
        if (n < 67) return false; // Solo primos grandes
        for (let i = 2; i <= Math.sqrt(n); i++) {
          if (n % i === 0) return false;
        }
        return true;
      };
      return isPrime(userStats.totalDailies);
    }
  },
  {
    id: "binary_prophet",
    name: "Profeta Binario",
    emoji: "💾",
    points: 64,
    description: "Completar daily cuando tu total de dailies sea una potencia de 2 ≥64",
    category: "Legendario",
    condition: (_, userStats) => {
      const n = userStats.totalDailies;
      return n >= 64 && (n & (n - 1)) === 0; // Solo potencias de 2 grandes
    }
  },
  {
    id: "time_traveler",
    name: "Viajero del Tiempo",
    emoji: "⏰",
    points: 88,
    description: "Completar daily a las 12:34:56",
    category: "Legendario",
    condition: (data) => {
      return data.timestamp.getHours() === 12 &&
             data.timestamp.getMinutes() === 34 &&
             data.timestamp.getSeconds() === 56;
    }
  },
  {
    id: "golden_ratio",
    name: "Ratio Dorado",
    emoji: "🏺",
    points: 161,
    description: "Completar daily cuando tu racha actual dividida por tu racha máxima se aproxime al ratio dorado (1.618)",
    category: "Legendario",
    condition: (_, userStats) => {
      if (userStats.maxStreak === 0) return false;
      const ratio = userStats.currentStreak / userStats.maxStreak;
      return Math.abs(ratio - 1.618) < 0.1; // Aproximación al ratio dorado
    }
  },
  {
    id: "leap_year_legend",
    name: "Leyenda del Año Bisiesto",
    emoji: "🦘",
    points: 29,
    description: "Completar daily el 29 de febrero",
    category: "Legendario",
    condition: (data) => {
      return data.timestamp.getMonth() === 1 && data.timestamp.getDate() === 29;
    }
  },
  {
    id: "century_marker",
    name: "Marcador del Siglo",
    emoji: "💯",
    points: 200,
    description: "Completar exactamente 100 dailies",
    category: "Legendario",
    condition: (_, userStats) => {
      return userStats.totalDailies === 100;
    }
  },
  {
    id: "perfect_storm",
    name: "Tormenta Perfecta",
    emoji: "⛈️",
    points: 300,
    description: "Completar daily en Lucky Day, usando palabra del día, con 7+ logros en un día",
    category: "Legendario",
    condition: (data, userStats) => {
      const {luckyDateStr, wordOfDay} = getConfigVars();
      const fechaStr = Utilities.formatDate(data.timestamp, Session.getScriptTimeZone(), "dd/MM/yyyy");
      const isLuckyDay = luckyDateStr === fechaStr;
      const usedWord = wordOfDay && new RegExp(`\\b${wordOfDay}\\b`, "i").test(data.ayer + " " + data.hoy + " " + data.imped);

      // Contar logros que se pueden obtener hoy
      let todayAchievements = 0;
      UNIQUE_ACHIEVEMENTS.forEach(ach => {
        if (ach.id !== "perfect_storm" && ach.condition(data, userStats)) {
          todayAchievements++;
        }
      });

      return isLuckyDay && usedWord && todayAchievements >= 7;
    }
  },

  // === LOGROS DE PURA SUERTE/COINCIDENCIA ===
  {
    id: "serendipity_master",
    name: "Maestro de la Serendipia",
    emoji: "🍀",
    points: 77,
    description: "Usar las palabras 'suerte', 'casualidad' y 'destino' en la misma daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return text.includes('suerte') && text.includes('casualidad') && text.includes('destino');
    }
  },
  {
    id: "matrix_glitch",
    name: "Glitch en la Matrix",
    emoji: "🔴",
    points: 99,
    description: "Mencionar 'bug', 'error' y 'matrix' en la misma daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return text.includes('bug') && text.includes('error') && text.includes('matrix');
    }
  },
  {
    id: "inception_dream",
    name: "Sueño de Inception",
    emoji: "🌀",
    points: 88,
    description: "Usar 'sueño', 'realidad' y 'despertar' en la misma daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return text.includes('sueño') && text.includes('realidad') && text.includes('despertar');
    }
  },
  {
    id: "time_paradox",
    name: "Paradoja Temporal",
    emoji: "⏳",
    points: 121,
    description: "Mencionar 'ayer', 'hoy' y 'mañana' exactamente en sus secciones correspondientes",
    category: "Legendario",
    condition: (data) => {
      return data.ayer.toLowerCase().includes('ayer') &&
             data.hoy.toLowerCase().includes('hoy') &&
             data.hoy.toLowerCase().includes('mañana');
    }
  },
  {
    id: "meta_achievement",
    name: "Logro Meta",
    emoji: "🎯",
    points: 111,
    description: "Mencionar 'logro', 'achievement' o 'badge' en tu daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return text.includes('logro') || text.includes('achievement') || text.includes('badge');
    }
  },
  {
    id: "recursive_daily",
    name: "Daily Recursiva",
    emoji: "🔄",
    points: 101,
    description: "Mencionar 'daily' en tu propia daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      return text.includes('daily');
    }
  },
  {
    id: "emoji_prophet",
    name: "Profeta de Emojis",
    emoji: "😎",
    points: 55,
    description: "Usar exactamente 5 emojis diferentes en tu daily",
    category: "Legendario",
    condition: (data) => {
      const text = data.ayer + " " + data.hoy + " " + data.imped;
      const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
      const emojis = text.match(emojiRegex) || [];
      const uniqueEmojis = [...new Set(emojis)];
      return uniqueEmojis.length === 5;
    }
  },
  {
    id: "number_mystic",
    name: "Místico de los Números",
    emoji: "🔢",
    points: 42,
    description: "Mencionar exactamente 3 números diferentes en tu daily",
    category: "Legendario",
    condition: (data) => {
      const text = data.ayer + " " + data.hoy + " " + data.imped;
      const numbers = text.match(/\b\d+\b/g) || [];
      const uniqueNumbers = [...new Set(numbers)];
      return uniqueNumbers.length === 3;
    }
  },
  {
    id: "color_spectrum",
    name: "Espectro de Colores",
    emoji: "🌈",
    points: 70,
    description: "Mencionar 4 colores diferentes en tu daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      const colors = ['rojo', 'azul', 'verde', 'amarillo', 'negro', 'blanco', 'rosa', 'morado', 'naranja', 'gris', 'marrón', 'violeta'];
      const foundColors = colors.filter(color => text.includes(color));
      return foundColors.length >= 4;
    }
  },
  {
    id: "weather_oracle",
    name: "Oráculo del Clima",
    emoji: "🌦️",
    points: 60,
    description: "Mencionar 3 fenómenos meteorológicos en tu daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      const weather = ['lluvia', 'sol', 'nieve', 'viento', 'tormenta', 'nube', 'niebla', 'granizo', 'rayo', 'trueno'];
      const foundWeather = weather.filter(w => text.includes(w));
      return foundWeather.length >= 3;
    }
  },
  {
    id: "animal_whisperer",
    name: "Susurrador de Animales",
    emoji: "🦁",
    points: 65,
    description: "Mencionar 5 animales diferentes en tu daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      const animals = ['perro', 'gato', 'león', 'tigre', 'elefante', 'mono', 'pájaro', 'pez', 'serpiente', 'oso', 'lobo', 'zorro', 'conejo', 'ratón', 'caballo', 'vaca', 'cerdo', 'oveja'];
      const foundAnimals = animals.filter(animal => text.includes(animal));
      return foundAnimals.length >= 5;
    }
  },
  {
    id: "tech_stack_master",
    name: "Maestro del Tech Stack",
    emoji: "💻",
    points: 80,
    description: "Mencionar 4 tecnologías de programación en tu daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      const techs = ['javascript', 'python', 'java', 'react', 'node', 'css', 'html', 'sql', 'git', 'docker', 'unity', 'c#', 'c++', 'php', 'angular', 'vue'];
      const foundTechs = techs.filter(tech => text.includes(tech));
      return foundTechs.length >= 4;
    }
  },
  {
    id: "food_critic",
    name: "Crítico Gastronómico",
    emoji: "🍕",
    points: 50,
    description: "Mencionar 6 comidas diferentes en tu daily",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      const foods = ['pizza', 'hamburguesa', 'pasta', 'arroz', 'pollo', 'pescado', 'ensalada', 'sopa', 'pan', 'queso', 'fruta', 'verdura', 'carne', 'huevo', 'leche', 'café', 'té'];
      const foundFoods = foods.filter(food => text.includes(food));
      return foundFoods.length >= 6;
    }
  },
  {
    id: "universe_philosopher",
    name: "Filósofo del Universo",
    emoji: "🌌",
    points: 137,
    description: "Usar palabras filosóficas profundas: 'existencia', 'consciencia', 'realidad', 'infinito'",
    category: "Legendario",
    condition: (data) => {
      const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
      const philosophical = ['existencia', 'consciencia', 'realidad', 'infinito'];
      return philosophical.every(word => text.includes(word));
    }
  },
  {
    id: "word_length_perfectionist",
    name: "Perfeccionista de Longitud",
    emoji: "📏",
    points: 123,
    description: "Escribir exactamente 123 caracteres en total (ayer + hoy + impedimentos)",
    category: "Legendario",
    condition: (data) => {
      const totalLength = (data.ayer + data.hoy + data.imped).length;
      return totalLength === 123;
    }
  }
];

// ------------------ HELPERS ------------------
function getOrCreateSheet(name, header) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  let sh = ss.getSheetByName(name);
  if (!sh) sh = ss.insertSheet(name);
  if (header && sh.getLastRow() === 0) sh.appendRow(header);
  return sh;
}

// Obtiene todos los participantes únicos desde las respuestas del formulario
function getAllParticipants() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const resp = ss.getSheetByName("Respuestas de formulario 1");
  if (!resp) return [];

  const data = resp.getDataRange().getValues();
  const header = data[0];
  const nameIdx = header.indexOf(NAME_FIELD);
  if (nameIdx === -1) return [];

  const participants = new Set();
  for (let i = 1; i < data.length; i++) {
    const name = String(data[i][nameIdx] || "").trim();
    if (name) participants.add(name);
  }

  return Array.from(participants).sort();
}

// Sistema de configuración flexible para puntos
function getGameConfig() {
  const configSheet = getOrCreateSheet("GameConfig", [
    "Regla", "Valor", "Activa", "Descripción"
  ]);

  // Configuración por defecto si la hoja está vacía
  if (configSheet.getLastRow() <= 1) {
    const defaultConfig = [
      ["PuntoBase", 1, true, "Punto por completar daily"],
      ["BonusLucky", 1, true, "Bonus en Lucky Day"],
      ["BonusWord", 1, true, "Bonus por usar palabra del día"],
      ["MultiplierWeekend", 1.5, false, "Multiplicador fin de semana"],
      ["BonusEarlyBird", 2, true, "Bonus por daily antes 9am"],
      ["BonusWeekendWarrior", 3, true, "Bonus por daily en fin de semana"],
      ["BonusDetailed", 1, true, "Bonus por daily detallado (+200 chars)"],
      ["BonusProjectWork", 3, true, "Bonus por mencionar trabajo del proyecto"],
      ["MultiplierProjectFocus", 1.5, true, "Multiplicador si daily es 80% sobre proyecto"]
    ];

    defaultConfig.forEach(row => configSheet.appendRow(row));
  }

  const data = configSheet.getDataRange().getValues();
  const config = {};

  for (let i = 1; i < data.length; i++) {
    const [regla, valor, activa] = data[i];
    if (activa) {
      config[regla] = Number(valor) || 0;
    }
  }

  return config;
}

// Detectar si la daily menciona trabajo del proyecto
function detectProjectWork(dailyData) {
  const text = (dailyData.ayer + " " + dailyData.hoy + " " + dailyData.imped).toLowerCase();

  const projectKeywords = [
    // Desarrollo
    'proyecto', 'feature', 'desarrollo', 'implementar', 'implementé', 'código', 'programar', 'programé',
    'build', 'crear', 'creé', 'funcionalidad', 'component', 'módulo', 'clase', 'función',

    // Resolución de problemas
    'solucion', 'arregl', 'fix', 'resolv', 'complet', 'termin', 'finaliz', 'acabé', 'acabar',
    'reparé', 'reparar', 'bug', 'error', 'problema', 'fallo', 'issue', 'defecto',

    // Testing y calidad
    'test', 'testing', 'prueba', 'probar', 'probé', 'verificar', 'verificué', 'validar', 'validé',
    'revisar', 'revisé', 'review', 'pull request', 'pr', 'merge', 'aprobar', 'aprobé',

    // Documentación y planificación
    'documentar', 'documenté', 'documentación', 'readme', 'wiki', 'comentar', 'comenté',
    'planificar', 'planifiqué', 'diseñar', 'diseñé', 'arquitectura', 'estructura',

    // Deploy y optimización
    'deploy', 'desplegar', 'desplegué', 'lanzar', 'lancé', 'publicar', 'publiqué', 'release',
    'optimizar', 'optimicé', 'mejorar', 'mejoré', 'refactor', 'refactorizar', 'performance'
  ];

  return projectKeywords.some(keyword => text.includes(keyword));
}

// Calcular el nivel de enfoque en el proyecto (0.0 a 1.0)
function calculateProjectFocus(dailyData) {
  const fullText = dailyData.ayer + " " + dailyData.hoy + " " + dailyData.imped;
  const totalWords = fullText.split(/\s+/).length;

  if (totalWords === 0) return 0;

  const projectText = fullText.toLowerCase();
  const projectKeywords = [
    'proyecto', 'feature', 'desarrollo', 'implementar', 'código', 'programar', 'build',
    'funcionalidad', 'component', 'módulo', 'test', 'testing', 'bug', 'fix', 'deploy'
  ];

  let projectWords = 0;
  projectKeywords.forEach(keyword => {
    const matches = (projectText.match(new RegExp(`\\b${keyword}`, 'g')) || []).length;
    projectWords += matches;
  });

  // Calcular porcentaje, máximo 1.0
  return Math.min(projectWords / totalWords * 3, 1.0); // *3 para dar más peso a palabras clave
}

// Sistema de gestión de logros únicos optimizado
let _achievementCache = null;
let _cacheTimestamp = 0;
const CACHE_DURATION = 30000; // 30 segundos

function getAchievementData() {
  const now = Date.now();
  if (_achievementCache && (now - _cacheTimestamp) < CACHE_DURATION) {
    return _achievementCache;
  }

  const achievementSheet = getOrCreateSheet("UserAchievements", [
    "Nombre", "LogroID", "Fecha", "Puntos", "Categoria", "Notificado"
  ]);

  _achievementCache = achievementSheet.getDataRange().getValues();
  _cacheTimestamp = now;
  return _achievementCache;
}

function clearAchievementCache() {
  _achievementCache = null;
  _cacheTimestamp = 0;
}

function getUserAchievements(userName) {
  const data = getAchievementData();
  const userAchievements = [];

  for (let i = 1; i < data.length; i++) {
    const [name, achievementId, date, points, category] = data[i];
    if (name === userName) {
      userAchievements.push({
        id: achievementId,
        date: date,
        points: points,
        category: category
      });
    }
  }

  return userAchievements;
}

function hasUserAchievement(userName, achievementId) {
  const data = getAchievementData();

  for (let i = 1; i < data.length; i++) {
    const [name, logId] = data[i];
    if (name === userName && logId === achievementId) {
      return true;
    }
  }

  return false;
}

function grantAchievement(userName, achievementId) {
  if (hasUserAchievement(userName, achievementId)) {
    return false; // Ya lo tiene
  }

  const achievement = UNIQUE_ACHIEVEMENTS.find(ach => ach.id === achievementId);
  if (!achievement) {
    return false; // Logro no existe
  }

  const achievementSheet = getOrCreateSheet("UserAchievements", [
    "Nombre", "LogroID", "Fecha", "Puntos", "Categoria", "Notificado"
  ]);

  achievementSheet.appendRow([
    userName,
    achievementId,
    new Date(),
    achievement.points,
    achievement.category,
    false // Pendiente de notificar
  ]);

  // Limpiar cache después de modificar
  clearAchievementCache();

  // NUEVO: Actualizar puntos totales en la hoja Gamification
  updateUserTotalPoints(userName);

  return true;
}

// Obtener logros pendientes de notificar para un usuario
function getPendingNotifications(userName) {
  const data = getAchievementData();
  const pendingAchievements = [];

  for (let i = 1; i < data.length; i++) {
    const [name, achievementId, date, , , notified] = data[i];
    if (name === userName && !notified) {
      const achievement = UNIQUE_ACHIEVEMENTS.find(ach => ach.id === achievementId);
      if (achievement) {
        pendingAchievements.push({
          ...achievement,
          dateEarned: date,
          rowIndex: i + 1 // Para marcar como notificado después
        });
      }
    }
  }

  return pendingAchievements;
}

// Marcar logros como notificados
function markAchievementsAsNotified(userName, achievementIds) {
  const achievementSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName("UserAchievements");
  if (!achievementSheet) return;

  const data = getAchievementData();
  const updates = [];

  for (let i = 1; i < data.length; i++) {
    const [name, achievementId] = data[i];
    if (name === userName && achievementIds.includes(achievementId)) {
      updates.push([i + 1, 6]); // Fila y columna para actualizar
    }
  }

  // Hacer todas las actualizaciones en batch
  updates.forEach(([row, col]) => {
    achievementSheet.getRange(row, col).setValue(true);
  });

  // Limpiar cache después de modificar
  if (updates.length > 0) {
    clearAchievementCache();
  }
}

function getUserAchievementEmojis(userName) {
  const userAchievements = getUserAchievements(userName);
  const emojis = [];

  userAchievements.forEach(userAch => {
    const achievement = UNIQUE_ACHIEVEMENTS.find(ach => ach.id === userAch.id);
    if (achievement) {
      emojis.push(achievement.emoji);
    }
  });

  return emojis.join("");
}

// Función para limpiar emojis duplicados
function removeDuplicateEmojis(text) {
  // Extraer todos los emojis del texto
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
  const emojis = text.match(emojiRegex) || [];

  // Crear un Set para eliminar duplicados manteniendo el orden
  const uniqueEmojis = [...new Set(emojis)];

  // Remover todos los emojis del texto original
  const textWithoutEmojis = text.replace(emojiRegex, '').replace(/\s+/g, ' ').trim();

  // Reconstruir con emojis únicos
  return uniqueEmojis.length > 0 ? `${textWithoutEmojis} ${uniqueEmojis.join('')}` : textWithoutEmojis;
}

// Función mejorada para crear nombre de display sin duplicados
function createDisplayName(userName, badge = "", extra = "") {
  const userAchievementEmojis = getUserAchievementEmojis(userName);

  // Combinar todos los emojis
  const allEmojis = `${userAchievementEmojis}${badge}${extra}`;

  // Limpiar duplicados
  const cleanEmojis = removeDuplicateEmojis(allEmojis);

  // Crear nombre final
  return cleanEmojis ? `${userName} ${cleanEmojis}` : userName;
}

// Función para recalcular y actualizar puntos totales de un usuario
function updateUserTotalPoints(userName) {
  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif) {
    console.log(`❌ Hoja ${GAMIF_SHEET_NAME} no encontrada`);
    return false;
  }

  // Encontrar la fila del usuario
  const data = gamif.getDataRange().getValues();
  let userRowIndex = -1;

  for (let i = 1; i < data.length; i++) {
    if (data[i][0] === userName) {
      userRowIndex = i + 1; // 1-based
      break;
    }
  }

  if (userRowIndex === -1) {
    console.log(`❌ Usuario ${userName} no encontrado en hoja Gamification`);
    return false;
  }

  // Obtener puntos actuales
  const currentPoints = gamif.getRange(userRowIndex, 4).getValue() || 0; // Columna "Puntos"

  // Calcular puntos totales de logros
  const userAchievements = getUserAchievements(userName);
  const achievementPoints = userAchievements.reduce((total, ach) => total + (ach.points || 0), 0);

  // Calcular puntos base (dailies completados * punto base)
  const gameConfig = getGameConfig();
  const totalDailies = gamif.getRange(userRowIndex, 3).getValue() || 0; // Columna "TotalDailies"
  const basePoints = totalDailies * (gameConfig.PuntoBase || 1);

  // Calcular otros bonos (Lucky Days, Palabras del día, etc.)
  // Para simplificar, calculamos la diferencia y asumimos que el resto son bonos
  const expectedMinimumPoints = basePoints + achievementPoints;
  const otherBonuses = Math.max(0, currentPoints - expectedMinimumPoints);

  // Nuevo total de puntos
  const newTotalPoints = basePoints + achievementPoints + otherBonuses;

  // Solo actualizar si hay diferencia
  if (newTotalPoints !== currentPoints) {
    const difference = newTotalPoints - currentPoints;

    console.log(`📊 ACTUALIZANDO PUNTOS DE ${userName}:`);
    console.log(`   Puntos actuales: ${currentPoints}`);
    console.log(`   Puntos base (${totalDailies} dailies): ${basePoints}`);
    console.log(`   Puntos por logros (${userAchievements.length} logros): ${achievementPoints}`);
    console.log(`   Otros bonos: ${otherBonuses}`);
    console.log(`   Nuevo total: ${newTotalPoints} (${difference > 0 ? '+' : ''}${difference})`);

    // Actualizar en la hoja
    gamif.getRange(userRowIndex, 4).setValue(newTotalPoints);

    return {
      userName,
      oldPoints: currentPoints,
      newPoints: newTotalPoints,
      difference: difference,
      achievementPoints: achievementPoints,
      basePoints: basePoints,
      otherBonuses: otherBonuses
    };
  }

  console.log(`✅ Puntos de ${userName} ya están actualizados (${currentPoints})`);
  return null;
}

// Función para recalcular puntos de todos los usuarios
function updateAllUserPoints() {
  console.log("🔄 RECALCULANDO PUNTOS DE TODOS LOS USUARIOS...");

  const participants = getAllParticipants();
  const results = [];
  let totalUpdated = 0;

  participants.forEach(userName => {
    const result = updateUserTotalPoints(userName);
    if (result) {
      results.push(result);
      totalUpdated++;
    }
  });

  console.log(`✅ Recálculo completado. ${totalUpdated} usuarios actualizados.`);

  if (results.length > 0) {
    console.log("\n📊 RESUMEN DE CAMBIOS:");
    results.forEach(result => {
      console.log(`   ${result.userName}: ${result.oldPoints} → ${result.newPoints} (${result.difference > 0 ? '+' : ''}${result.difference})`);
    });
  }

  return results;
}

/* ==========================================================
 * 9) SISTEMA DE ESCUDO DE RACHA
 * ========================================================== */

// Obtener estado del escudo de un usuario
function getShieldStatus(userName) {
  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif) return null;

  const data = gamif.getDataRange().getValues();
  for (let i = 1; i < data.length; i++) {
    if (data[i][0] === userName) {
      // FIXED: Convertir timestamp de vuelta a Date si es necesario
      let lastDate = data[i][6] || null;
      if (lastDate && typeof lastDate === 'number') {
        lastDate = new Date(lastDate);
      } else if (lastDate && typeof lastDate === 'string') {
        lastDate = parseDateStr(lastDate);
      }

      return {
        rowIndex: i + 1,
        shieldAvailable: data[i][10] !== false, // ShieldAvailable
        shieldLock: data[i][11] === true,       // ShieldLock
        shieldLastUsed: data[i][12] || null,    // ShieldLastUsed
        currentStreak: data[i][4] || 0,         // Streak
        lastDate: lastDate                      // UltimaFecha (convertida correctamente)
      };
    }
  }
  return null;
}

// Establecer estado del escudo de un usuario
function setShieldStatus(userName, shieldAvailable, shieldLock, shieldLastUsed = null) {
  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif) return false;

  const data = gamif.getDataRange().getValues();
  for (let i = 1; i < data.length; i++) {
    if (data[i][0] === userName) {
      const rowIndex = i + 1;
      gamif.getRange(rowIndex, 11).setValue(shieldAvailable);  // ShieldAvailable
      gamif.getRange(rowIndex, 12).setValue(shieldLock);       // ShieldLock
      if (shieldLastUsed !== null) {
        gamif.getRange(rowIndex, 13).setValue(shieldLastUsed); // ShieldLastUsed
      }
      return true;
    }
  }
  return false;
}

// Consumir escudo de un usuario (estilo Duolingo)
function consumeShield(userName) {
  const shieldStatus = getShieldStatus(userName);
  if (!shieldStatus || !shieldStatus.shieldAvailable) {
    return false;
  }

  const today = new Date();
  // CAMBIO: En lugar de lock=true, ponemos available=false (como Duolingo)
  // El usuario necesitará alcanzar 7 días de racha nuevamente para obtener otro escudo
  setShieldStatus(userName, false, false, today);

  console.log(`🛡️ Escudo consumido para ${userName} - necesitará 7 días de racha para obtener otro`);
  return true;
}

// Otorgar escudo automáticamente (7 días inicial, 2 días después de usar uno)
function grantShieldOnStreak(userName, currentStreak) {
  const shieldStatus = getShieldStatus(userName);
  if (!shieldStatus) return false;

  // No otorgar si ya tiene escudo disponible
  if (shieldStatus.shieldAvailable) return false;

  let shouldGrantShield = false;
  let reason = "";

  // Verificar si nunca ha tenido escudo (primera vez)
  const hasUsedShieldBefore = shieldStatus.shieldLastUsed !== null && shieldStatus.shieldLastUsed !== "";

  if (!hasUsedShieldBefore) {
    // Primera vez: necesita 7 días consecutivos
    if (currentStreak === 7) {
      shouldGrantShield = true;
      reason = "7 días consecutivos (primera vez)";
    }
  } else {
    // Ya usó escudo antes: solo necesita 2 días consecutivos
    if (currentStreak >= 2) {
      shouldGrantShield = true;
      reason = `${currentStreak} días consecutivos (regeneración rápida)`;
    }
  }

  if (shouldGrantShield) {
    setShieldStatus(userName, true, false); // Disponible=true, Lock=false
    console.log(`🛡️ ¡${userName} ha ganado un Escudo de Racha! (${reason})`);

    // Notificar a Discord
    const userEmojis = getUserAchievementEmojis(userName);
    const displayName = userEmojis ? `${userName} ${userEmojis}` : userName;

    const msg = hasUsedShieldBefore ?
      `🛡️ **¡${displayName} ha regenerado su Escudo de Racha!**\n` +
      `⚡ **${currentStreak} días consecutivos** después de usar su escudo anterior!\n` +
      `💪 El escudo protegerá automáticamente tu racha si un día no puedes hacer tu daily.` :
      `🛡️ **¡${displayName} ha ganado su primer Escudo de Racha!**\n` +
      `🔥 **7 días consecutivos** de dailies completados!\n` +
      `💪 El escudo protegerá automáticamente tu racha si un día no puedes hacer tu daily.`;

    UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
      method: "post",
      contentType: "application/json",
      payload: JSON.stringify({ content: msg })
    });

    // Pequeño delay para evitar conflictos con otros mensajes de Discord
    Utilities.sleep(1000); // 1 segundo

    return true;
  }

  return false;
}

// Verificar si un usuario ha enviado daily en las últimas 24 horas
function hasRecentDaily(userName, checkTime = new Date()) {
  const shieldStatus = getShieldStatus(userName);
  if (!shieldStatus || !shieldStatus.lastDate) {
    console.log(`🔍 ${userName}: Sin fecha de última daily`);
    return false;
  }

  // FIXED: Mejor manejo de conversión de fechas
  let lastDaily = shieldStatus.lastDate;
  if (!(lastDaily instanceof Date)) {
    if (typeof lastDaily === 'number') {
      lastDaily = new Date(lastDaily);
    } else if (typeof lastDaily === 'string') {
      lastDaily = parseDateStr(lastDaily);
    } else {
      console.log(`🔍 ${userName}: Formato de fecha inválido: ${lastDaily}`);
      return false;
    }
  }

  if (!lastDaily || isNaN(lastDaily.getTime())) {
    console.log(`🔍 ${userName}: Fecha inválida después de conversión`);
    return false;
  }

  const timeDiff = checkTime - lastDaily;
  const hoursDiff = timeDiff / (1000 * 60 * 60);

  console.log(`🔍 ${userName}: Última daily: ${lastDaily.toLocaleString()}, Hace ${hoursDiff.toFixed(1)} horas`);

  return hoursDiff < 24;
}

// Función principal para aplicar escudos automáticamente (ejecutada por trigger a las 08:00)
function applyStreakShields() {
  console.log("🛡️ APLICANDO ESCUDOS DE RACHA AUTOMÁTICOS...");

  const participants = getAllParticipants();
  const checkTime = new Date();
  let shieldsApplied = 0;
  let shieldsSkipped = 0;

  participants.forEach(userName => {
    try {
      const shieldStatus = getShieldStatus(userName);

      if (!shieldStatus) {
        console.log(`❌ ${userName}: No se pudo obtener estado del escudo`);
        shieldsSkipped++;
        return;
      }

      // Verificar condiciones para aplicar escudo (estilo Duolingo)
      const hasStreak = shieldStatus.currentStreak > 0;
      const hasNoRecentDaily = !hasRecentDaily(userName, checkTime);
      const hasShieldAvailable = shieldStatus.shieldAvailable; // Solo verificar si tiene escudo disponible

      console.log(`📊 ${userName}: Streak=${shieldStatus.currentStreak}, NoDaily=${hasNoRecentDaily}, Shield=${hasShieldAvailable}`);
      console.log(`🔍 ${userName}: UltimaFecha raw: ${shieldStatus.lastDate}, tipo: ${typeof shieldStatus.lastDate}`);

      // DEBUG ADICIONAL: Mostrar fecha parseada y cálculo de horas
      if (shieldStatus.lastDate) {
        let lastDaily = shieldStatus.lastDate;
        if (typeof lastDaily === 'number') {
          lastDaily = new Date(lastDaily);
        } else if (typeof lastDaily === 'string') {
          lastDaily = parseDateStr(lastDaily);
        }

        if (lastDaily && !isNaN(lastDaily.getTime())) {
          const timeDiff = checkTime - lastDaily;
          const hoursDiff = timeDiff / (1000 * 60 * 60);
          console.log(`🕐 ${userName}: Última daily: ${lastDaily.toLocaleString()}, hace ${hoursDiff.toFixed(1)}h`);
        } else {
          console.log(`❌ ${userName}: Fecha inválida después de conversión`);
        }
      }

      if (hasStreak && hasNoRecentDaily && hasShieldAvailable) {
        // Aplicar escudo
        if (consumeShield(userName)) {
          shieldsApplied++;

          // Enviar notificación a Discord
          const userEmojis = getUserAchievementEmojis(userName);
          const displayName = userEmojis ? `${userName} ${userEmojis}` : userName;
          const msg = `🛡️ **${displayName}** ha usado su Escudo de Racha automáticamente!\n` +
                     `⚡ Mantiene su racha de **${shieldStatus.currentStreak} días** viva!\n` +
                     `💡 Recuerda enviar tu daily hoy para continuar la racha.`;

          UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
            method: "post",
            contentType: "application/json",
            payload: JSON.stringify({ content: msg })
          });

          console.log(`✅ ${userName}: Escudo aplicado exitosamente`);
        } else {
          console.log(`❌ ${userName}: Error al aplicar escudo`);
          shieldsSkipped++;
        }
      } else {
        let reason = [];
        if (!hasStreak) reason.push("sin racha");
        if (!hasNoRecentDaily) reason.push("daily reciente");
        if (!hasShieldAvailable) reason.push("sin escudo disponible");

        console.log(`⏭️ ${userName}: Escudo no aplicado (${reason.join(", ")})`);
        shieldsSkipped++;
      }

    } catch (error) {
      console.log(`❌ Error procesando ${userName}: ${error.message}`);
      shieldsSkipped++;
    }
  });

  console.log(`🛡️ Proceso completado: ${shieldsApplied} escudos aplicados, ${shieldsSkipped} omitidos`);

  // Enviar resumen si se aplicaron escudos
  if (shieldsApplied > 0) {
    const summaryMsg = `📊 **Resumen de Escudos Automáticos**\n` +
                      `🛡️ **${shieldsApplied}** escudos aplicados automáticamente\n` +
                      `⏰ Verificación realizada a las 08:00 AM\n` +
                      `💡 Los usuarios protegidos deben enviar su daily hoy para mantener la racha.`;

    UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
      method: "post",
      contentType: "application/json",
      payload: JSON.stringify({ content: summaryMsg })
    });
  }

  return { shieldsApplied, shieldsSkipped };
}

// Función para inicializar el sistema de escudos estilo Duolingo
function initializeDuolingoShieldSystem() {
  console.log("🛡️ INICIALIZANDO SISTEMA DE ESCUDOS ESTILO DUOLINGO...");

  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif) {
    console.log("❌ Hoja Gamification no encontrada");
    return false;
  }

  const data = gamif.getDataRange().getValues();
  let initializedCount = 0;

  for (let i = 1; i < data.length; i++) {
    const userName = data[i][0];
    const currentStreak = data[i][4] || 0;

    if (!userName) continue;

    // Otorgar escudo si ya tiene 7+ días de racha
    if (currentStreak >= 7) {
      // Verificar si ya tiene escudo
      const shieldStatus = getShieldStatus(userName);
      if (shieldStatus && !shieldStatus.shieldAvailable) {
        setShieldStatus(userName, true, false); // Disponible=true, Lock=false
        console.log(`🛡️ ${userName}: Escudo otorgado (racha actual: ${currentStreak} días)`);
        initializedCount++;
      } else {
        console.log(`✅ ${userName}: Ya tiene escudo (racha: ${currentStreak} días)`);
      }
    } else {
      // Asegurar que no tenga escudo si no tiene 7+ días
      const shieldStatus = getShieldStatus(userName);
      if (shieldStatus && shieldStatus.shieldAvailable) {
        setShieldStatus(userName, false, false);
        console.log(`❌ ${userName}: Escudo removido (racha insuficiente: ${currentStreak} días)`);
      }
    }
  }

  console.log(`✅ Sistema inicializado: ${initializedCount} escudos otorgados`);

  // Enviar notificación a Discord
  const msg = `🛡️ **Sistema de Escudos actualizado (mejorado)!**\n\n` +
             `📋 **Cómo funciona:**\n` +
             `• 🔥 **Primer escudo**: Alcanza 7 días consecutivos de dailies\n` +
             `• 🛡️ **Protección automática**: Si un día no haces daily, el escudo se usa automáticamente\n` +
             `• ⚡ **Regeneración rápida**: Solo necesitas 2 días consecutivos para obtener otro escudo\n` +
             `• 📊 **Solo 1 escudo**: No puedes acumular múltiples escudos\n\n` +
             `${initializedCount} usuarios recibieron escudos basados en sus rachas actuales.`;

  UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
    method: "post",
    contentType: "application/json",
    payload: JSON.stringify({ content: msg })
  });

  return initializedCount;
}

// Función para migrar fechas existentes de formato string a timestamp
function migrateDateFormatsToTimestamp() {
  console.log("🔄 MIGRANDO FORMATOS DE FECHA A TIMESTAMP...");

  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif) {
    console.log("❌ Hoja Gamification no encontrada");
    return false;
  }

  const data = gamif.getDataRange().getValues();
  let migratedCount = 0;

  for (let i = 1; i < data.length; i++) {
    const userName = data[i][0];
    const rawLastDate = data[i][6]; // Columna UltimaFecha

    if (!userName || !rawLastDate) continue;

    // Si ya es un número (timestamp), no hacer nada
    if (typeof rawLastDate === 'number') {
      console.log(`✅ ${userName}: Ya tiene timestamp (${rawLastDate})`);
      continue;
    }

    // Si es string o Date, convertir a timestamp
    let dateObj = null;
    if (rawLastDate instanceof Date) {
      dateObj = rawLastDate;
    } else if (typeof rawLastDate === 'string') {
      dateObj = parseDateStr(rawLastDate);
    }

    if (dateObj && !isNaN(dateObj.getTime())) {
      const timestamp = dateObj.getTime();
      gamif.getRange(i + 1, 7).setValue(timestamp); // Columna G (UltimaFecha)
      console.log(`🔄 ${userName}: ${rawLastDate} → ${timestamp} (${dateObj.toLocaleString()})`);
      migratedCount++;
    } else {
      console.log(`❌ ${userName}: No se pudo convertir fecha: ${rawLastDate}`);
    }
  }

  console.log(`✅ Migración completada: ${migratedCount} fechas convertidas a timestamp`);
  return migratedCount;
}

// Función para diagnosticar el problema de fechas en el sistema de escudos
function debugShieldDateIssue() {
  console.log("🔍 DIAGNOSTICANDO PROBLEMA DE FECHAS EN ESCUDOS...");

  const participants = ["Ian", "Rubén", "Dani", "Laura", "Camilo", "Paola"];
  const checkTime = new Date("2025-07-31T08:20:00"); // Hora de aplicación de escudos

  participants.forEach(userName => {
    console.log(`\n--- DIAGNÓSTICO PARA ${userName} ---`);

    const shieldStatus = getShieldStatus(userName);
    if (!shieldStatus) {
      console.log(`❌ No se pudo obtener estado del escudo`);
      return;
    }

    console.log(`📊 Datos del escudo:`);
    console.log(`   - Streak: ${shieldStatus.currentStreak}`);
    console.log(`   - Shield Available: ${shieldStatus.shieldAvailable}`);
    console.log(`   - Shield Lock: ${shieldStatus.shieldLock}`);
    console.log(`   - Last Date Raw: ${shieldStatus.lastDate}`);
    console.log(`   - Last Date Type: ${typeof shieldStatus.lastDate}`);

    if (shieldStatus.lastDate) {
      const lastDaily = shieldStatus.lastDate instanceof Date ? shieldStatus.lastDate : new Date(shieldStatus.lastDate);
      console.log(`   - Last Date Parsed: ${lastDaily.toLocaleString()}`);
      console.log(`   - Last Date ISO: ${lastDaily.toISOString()}`);

      const timeDiff = checkTime - lastDaily;
      const hoursDiff = timeDiff / (1000 * 60 * 60);
      console.log(`   - Time Diff: ${timeDiff}ms`);
      console.log(`   - Hours Diff: ${hoursDiff.toFixed(2)} horas`);
      console.log(`   - Has Recent Daily: ${hoursDiff < 24}`);
    }

    // Verificar también en la hoja directamente
    const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
    if (gamif) {
      const data = gamif.getDataRange().getValues();
      for (let i = 1; i < data.length; i++) {
        if (data[i][0] === userName) {
          console.log(`📋 Datos directos de la hoja:`);
          console.log(`   - UltimaFecha (col 6): ${data[i][6]}`);
          console.log(`   - UltimaFecha type: ${typeof data[i][6]}`);
          if (data[i][6] instanceof Date) {
            console.log(`   - UltimaFecha ISO: ${data[i][6].toISOString()}`);
          }
          break;
        }
      }
    }
  });

  console.log("\n🔍 DIAGNÓSTICO COMPLETADO");
}

// Función para verificar manualmente el problema de escudos aplicados incorrectamente
function debugIncorrectShieldApplication() {
  console.log("🔍 VERIFICANDO PROBLEMA DE ESCUDOS APLICADOS INCORRECTAMENTE");
  console.log("=" .repeat(70));

  const problematicUsers = ["Ian", "Laura", "Paola"];
  const checkTime = new Date("2025-01-09T08:20:00"); // Hora cuando se aplicaron escudos

  console.log(`⏰ Hora de verificación simulada: ${checkTime.toLocaleString()}`);
  console.log("");

  problematicUsers.forEach(userName => {
    console.log(`--- ANÁLISIS PARA ${userName} ---`);

    // Obtener estado actual del escudo
    const shieldStatus = getShieldStatus(userName);
    if (!shieldStatus) {
      console.log(`❌ No se pudo obtener estado del escudo`);
      return;
    }

    console.log(`📊 Estado del escudo:`);
    console.log(`   - Streak: ${shieldStatus.currentStreak}`);
    console.log(`   - Shield Available: ${shieldStatus.shieldAvailable}`);
    console.log(`   - Last Date Raw: ${shieldStatus.lastDate}`);
    console.log(`   - Last Date Type: ${typeof shieldStatus.lastDate}`);

    // Convertir fecha
    let lastDaily = shieldStatus.lastDate;
    if (typeof lastDaily === 'number') {
      lastDaily = new Date(lastDaily);
    } else if (typeof lastDaily === 'string') {
      lastDaily = parseDateStr(lastDaily);
    }

    if (lastDaily && !isNaN(lastDaily.getTime())) {
      console.log(`📅 Última daily parseada: ${lastDaily.toLocaleString()}`);

      const timeDiff = checkTime - lastDaily;
      const hoursDiff = timeDiff / (1000 * 60 * 60);
      console.log(`⏱️  Diferencia: ${hoursDiff.toFixed(1)} horas`);
      console.log(`✅ ¿Tiene daily reciente? (< 24h): ${hoursDiff < 24}`);
      console.log(`🛡️ ¿Debería aplicar escudo?: ${hoursDiff >= 24}`);

      // Verificar si el cálculo es correcto
      if (hoursDiff < 24) {
        console.log(`🚨 PROBLEMA: ${userName} tenía daily reciente pero se aplicó escudo`);
      } else {
        console.log(`✅ CORRECTO: ${userName} no tenía daily reciente, escudo aplicado correctamente`);
      }
    } else {
      console.log(`❌ No se pudo parsear la fecha`);
    }

    console.log("");
  });

  console.log("🎯 CONCLUSIÓN:");
  console.log("Si algún usuario muestra 'PROBLEMA', hay un bug en la lógica de detección.");
  console.log("Si todos muestran 'CORRECTO', el problema puede estar en los datos de la hoja.");

  return true;
}

// Función para verificar timestamps actuales en la hoja de gamificación
function checkCurrentTimestamps() {
  console.log("📅 VERIFICANDO TIMESTAMPS ACTUALES EN LA HOJA");
  console.log("=" .repeat(60));

  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif) {
    console.log("❌ Hoja Gamification no encontrada");
    return false;
  }

  const data = gamif.getDataRange().getValues();
  const now = new Date();

  console.log(`⏰ Hora actual: ${now.toLocaleString()}`);
  console.log("");

  for (let i = 1; i < data.length; i++) {
    const userName = data[i][0];
    const rawTimestamp = data[i][6]; // Columna UltimaFecha

    if (!userName) continue;

    console.log(`--- ${userName} ---`);
    console.log(`📊 Timestamp raw: ${rawTimestamp}`);
    console.log(`📊 Tipo: ${typeof rawTimestamp}`);

    if (rawTimestamp) {
      let parsedDate = null;

      if (typeof rawTimestamp === 'number') {
        parsedDate = new Date(rawTimestamp);
      } else if (typeof rawTimestamp === 'string') {
        parsedDate = parseDateStr(rawTimestamp);
      } else if (rawTimestamp instanceof Date) {
        parsedDate = rawTimestamp;
      }

      if (parsedDate && !isNaN(parsedDate.getTime())) {
        console.log(`📅 Fecha parseada: ${parsedDate.toLocaleString()}`);

        const timeDiff = now - parsedDate;
        const hoursDiff = timeDiff / (1000 * 60 * 60);
        console.log(`⏱️  Hace: ${hoursDiff.toFixed(1)} horas`);
        console.log(`✅ ¿Es reciente? (< 24h): ${hoursDiff < 24}`);
      } else {
        console.log(`❌ No se pudo parsear la fecha`);
      }
    } else {
      console.log(`❌ Sin timestamp`);
    }

    console.log("");
  }

  return true;
}

// Función para limpiar Invalid Dates en la hoja de gamificación
function fixInvalidDatesInSheet() {
  console.log("🔧 LIMPIANDO INVALID DATES EN LA HOJA...");

  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif) {
    console.log("❌ Hoja Gamification no encontrada");
    return false;
  }

  const data = gamif.getDataRange().getValues();
  let fixedCount = 0;

  for (let i = 1; i < data.length; i++) {
    const userName = data[i][0];
    const rawTimestamp = data[i][6]; // Columna UltimaFecha

    if (!userName) continue;

    console.log(`🔍 ${userName}: Timestamp = ${rawTimestamp}, Tipo = ${typeof rawTimestamp}`);

    // Si es Invalid Date o string vacío, intentar recuperar de las respuestas del formulario
    if (!rawTimestamp || rawTimestamp.toString() === "Invalid Date" || rawTimestamp === "") {
      console.log(`🔧 ${userName}: Intentando recuperar fecha de respuestas del formulario...`);

      // Buscar la última respuesta de este usuario en el formulario
      const resp = SpreadsheetApp.getActiveSpreadsheet().getSheetByName("Respuestas de formulario 1");
      if (resp) {
        const respData = resp.getDataRange().getValues();
        const header = respData[0];
        const nameIdx = header.indexOf(NAME_FIELD);
        const timeIdx = header.indexOf("Marca temporal");

        // Buscar la última respuesta de este usuario
        let lastResponse = null;
        for (let j = respData.length - 1; j >= 1; j--) {
          if (respData[j][nameIdx] === userName) {
            lastResponse = respData[j][timeIdx];
            break;
          }
        }

        if (lastResponse) {
          console.log(`📅 ${userName}: Encontrada respuesta: ${lastResponse}`);

          // Intentar parsear la fecha de la respuesta
          let recoveredDate = new Date(lastResponse);
          if (isNaN(recoveredDate.getTime())) {
            recoveredDate = parseDateStr(lastResponse.toString());
          }

          if (recoveredDate && !isNaN(recoveredDate.getTime())) {
            const timestamp = recoveredDate.getTime();
            gamif.getRange(i + 1, 7).setValue(timestamp); // Actualizar con timestamp
            console.log(`✅ ${userName}: Fecha recuperada y actualizada: ${recoveredDate.toLocaleString()}`);
            fixedCount++;
          } else {
            console.log(`❌ ${userName}: No se pudo parsear fecha de respuesta`);
          }
        } else {
          console.log(`❌ ${userName}: No se encontró respuesta en formulario`);
        }
      }
    } else {
      console.log(`✅ ${userName}: Timestamp válido`);
    }
  }

  console.log(`🎯 LIMPIEZA COMPLETADA: ${fixedCount} fechas reparadas`);

  if (fixedCount > 0) {
    console.log("💡 Recomendación: Ejecutar showAllShieldStatus() para verificar el estado actualizado");
  }

  return fixedCount;
}

// Función para reactivar escudos consumidos incorrectamente por el bug de timestamps
function restoreIncorrectlyConsumedShields() {
  console.log("🛡️ RESTAURANDO ESCUDOS CONSUMIDOS INCORRECTAMENTE...");

  // Usuarios que fueron afectados por el bug del 02/08/2025 a las 8:20 AM
  const affectedUsers = [
    { name: "Ian" },
    { name: "Laura" },
    { name: "Paola" }
  ];

  // Fecha del bug (02/08/2025 entre 8:00-8:30 AM)
  const bugDateStart = new Date("2025-08-02T08:00:00");
  const bugDateEnd = new Date("2025-08-02T08:30:00");

  let restoredCount = 0;

  affectedUsers.forEach(user => {
    console.log(`\n--- RESTAURANDO ESCUDO PARA ${user.name} ---`);

    const shieldStatus = getShieldStatus(user.name);
    if (!shieldStatus) {
      console.log(`❌ ${user.name}: No se pudo obtener estado del escudo`);
      return;
    }

    console.log(`📊 Estado actual:`);
    console.log(`   - Streak: ${shieldStatus.currentStreak}`);
    console.log(`   - Shield Available: ${shieldStatus.shieldAvailable}`);
    console.log(`   - Shield Last Used: ${shieldStatus.shieldLastUsed}`);

    // Verificar si el escudo fue consumido durante el bug (02/08/2025 8:00-8:30 AM)
    let wasBugVictim = false;
    if (shieldStatus.shieldLastUsed) {
      let lastUsedDate = shieldStatus.shieldLastUsed;
      if (typeof lastUsedDate === 'string') {
        lastUsedDate = new Date(lastUsedDate);
      }

      if (lastUsedDate && !isNaN(lastUsedDate.getTime())) {
        wasBugVictim = lastUsedDate >= bugDateStart && lastUsedDate <= bugDateEnd;
        console.log(`🔍 ${user.name}: Escudo usado el ${lastUsedDate.toLocaleString()}, ¿Es víctima del bug? ${wasBugVictim}`);
      }
    }

    if (wasBugVictim && !shieldStatus.shieldAvailable) {
      // Restaurar escudo
      setShieldStatus(user.name, true, false, null); // Available=true, Lock=false, LastUsed=null
      console.log(`✅ ${user.name}: Escudo restaurado (víctima del bug de timestamps)`);
      restoredCount++;

      // Notificar en Discord
      const userEmojis = getUserAchievementEmojis(user.name);
      const displayName = userEmojis ? `${user.name} ${userEmojis}` : user.name;

      const msg = `🛡️ **Escudo Restaurado por Error del Sistema**\n\n` +
                 `${displayName} ha recuperado su escudo que fue consumido incorrectamente el 02/08/2025.\n\n` +
                 `🔧 **Causa**: Bug en el sistema de timestamps que aplicó escudos a usuarios que SÍ habían enviado dailies.\n` +
                 `✅ **Solución**: Escudo restaurado automáticamente.\n` +
                 `💪 Tu escudo está listo para proteger tu racha cuando lo necesites.`;

      try {
        UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
          method: "post",
          contentType: "application/json",
          payload: JSON.stringify({ content: msg })
        });
        console.log(`📤 ${user.name}: Notificación enviada a Discord`);
      } catch (error) {
        console.log(`❌ ${user.name}: Error enviando notificación: ${error.message}`);
      }

      // Pequeño delay entre notificaciones
      Utilities.sleep(1000);

    } else if (shieldStatus.shieldAvailable) {
      console.log(`✅ ${user.name}: Ya tiene escudo disponible`);
    } else if (!wasBugVictim) {
      console.log(`⚠️ ${user.name}: Escudo no fue consumido en la fecha del bug`);
    }
  });

  console.log(`\n🎯 RESTAURACIÓN COMPLETADA: ${restoredCount} escudos restaurados`);

  if (restoredCount > 0) {
    // Enviar resumen general
    const summaryMsg = `📊 **Resumen de Restauración de Escudos**\n\n` +
                      `🔧 Se han restaurado ${restoredCount} escudos que fueron consumidos incorrectamente debido a un bug del sistema.\n\n` +
                      `✅ **Usuarios afectados**: ${affectedUsers.slice(0, restoredCount).map(u => u.name).join(", ")}\n` +
                      `🛡️ **Estado**: Todos los escudos han sido restaurados y están listos para usar.\n` +
                      `💡 **Prevención**: El bug ha sido solucionado y no volverá a ocurrir.`;

    try {
      UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
        method: "post",
        contentType: "application/json",
        payload: JSON.stringify({ content: summaryMsg })
      });
      console.log(`📤 Resumen general enviado a Discord`);
    } catch (error) {
      console.log(`❌ Error enviando resumen: ${error.message}`);
    }
  }

  return restoredCount;
}

// Función para restaurar escudo manualmente a un usuario específico
function manualRestoreShield(userName, reason = "Restauración manual") {
  console.log(`🛡️ RESTAURANDO ESCUDO MANUALMENTE PARA ${userName}...`);

  const shieldStatus = getShieldStatus(userName);
  if (!shieldStatus) {
    console.log(`❌ ${userName}: No se pudo obtener estado del escudo`);
    return false;
  }

  console.log(`📊 Estado actual:`);
  console.log(`   - Streak: ${shieldStatus.currentStreak}`);
  console.log(`   - Shield Available: ${shieldStatus.shieldAvailable}`);
  console.log(`   - Shield Last Used: ${shieldStatus.shieldLastUsed}`);

  if (shieldStatus.shieldAvailable) {
    console.log(`⚠️ ${userName}: Ya tiene escudo disponible`);
    return false;
  }

  // Restaurar escudo
  setShieldStatus(userName, true, false, null); // Available=true, Lock=false, LastUsed=null
  console.log(`✅ ${userName}: Escudo restaurado manualmente`);

  // Notificar en Discord
  const userEmojis = getUserAchievementEmojis(userName);
  const displayName = userEmojis ? `${userName} ${userEmojis}` : userName;

  const msg = `🛡️ **Escudo Restaurado Manualmente**\n\n` +
             `${displayName} ha recibido un escudo.\n\n` +
             `📝 **Razón**: ${reason}\n` +
             `💪 Tu escudo está listo para proteger tu racha cuando lo necesites.`;

  try {
    UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
      method: "post",
      contentType: "application/json",
      payload: JSON.stringify({ content: msg })
    });
    console.log(`📤 ${userName}: Notificación enviada a Discord`);
  } catch (error) {
    console.log(`❌ ${userName}: Error enviando notificación: ${error.message}`);
  }

  return true;
}

// Función para restaurar escudos a múltiples usuarios
function bulkRestoreShields(userNames, reason = "Restauración masiva") {
  console.log(`🛡️ RESTAURACIÓN MASIVA DE ESCUDOS...`);
  console.log(`👥 Usuarios: ${userNames.join(", ")}`);
  console.log(`📝 Razón: ${reason}`);

  let restoredCount = 0;

  userNames.forEach(userName => {
    if (manualRestoreShield(userName, reason)) {
      restoredCount++;
    }
    Utilities.sleep(500); // Delay entre restauraciones
  });

  console.log(`🎯 RESTAURACIÓN COMPLETADA: ${restoredCount}/${userNames.length} escudos restaurados`);
  return restoredCount;
}

// Función simple para restaurar escudos de las víctimas del bug de timestamps
function fixBugVictimsShields() {
  console.log("🛡️ RESTAURANDO ESCUDOS DE VÍCTIMAS DEL BUG DE TIMESTAMPS...");

  const bugVictims = ["Ian", "Laura", "Paola"];
  const reason = "Bug de timestamps del 02/08/2025 solucionado";

  console.log(`👥 Víctimas identificadas: ${bugVictims.join(", ")}`);
  console.log(`📝 Razón: ${reason}`);

  let restoredCount = 0;

  bugVictims.forEach(userName => {
    console.log(`\n🔧 Restaurando escudo para ${userName}...`);

    const shieldStatus = getShieldStatus(userName);
    if (!shieldStatus) {
      console.log(`❌ ${userName}: No se pudo obtener estado`);
      return;
    }

    if (shieldStatus.shieldAvailable) {
      console.log(`✅ ${userName}: Ya tiene escudo disponible`);
      return;
    }

    // Restaurar escudo directamente
    setShieldStatus(userName, true, false, null);
    console.log(`✅ ${userName}: Escudo restaurado exitosamente`);
    restoredCount++;

    // Notificar individualmente
    const userEmojis = getUserAchievementEmojis(userName);
    const displayName = userEmojis ? `${userName} ${userEmojis}` : userName;

    const msg = `🛡️ **Escudo Restaurado - Disculpas por el Error**\n\n` +
               `${displayName}, tu escudo ha sido restaurado.\n\n` +
               `🔧 **Qué pasó**: Un bug en el sistema aplicó tu escudo incorrectamente el 02/08/2025, aunque SÍ habías enviado tu daily.\n\n` +
               `✅ **Solucionado**: El bug ha sido reparado y tu escudo restaurado.\n` +
               `💪 Tu escudo está listo para proteger tu racha cuando realmente lo necesites.\n\n` +
               `🙏 Disculpas por las molestias.`;

    try {
      UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
        method: "post",
        contentType: "application/json",
        payload: JSON.stringify({ content: msg })
      });
      console.log(`📤 ${userName}: Notificación enviada`);
    } catch (error) {
      console.log(`❌ ${userName}: Error en notificación: ${error.message}`);
    }

    Utilities.sleep(1000); // Delay entre notificaciones
  });

  // Resumen final
  if (restoredCount > 0) {
    const summaryMsg = `📊 **Restauración de Escudos Completada**\n\n` +
                      `✅ **Escudos restaurados**: ${restoredCount}\n` +
                      `👥 **Usuarios**: ${bugVictims.slice(0, restoredCount).join(", ")}\n\n` +
                      `🔧 **Causa del bug**: Sistema de timestamps defectuoso\n` +
                      `✅ **Estado**: Bug solucionado, sistema funcionando correctamente\n` +
                      `🛡️ **Resultado**: Todos los escudos restaurados y listos para usar`;

    try {
      UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
        method: "post",
        contentType: "application/json",
        payload: JSON.stringify({ content: summaryMsg })
      });
      console.log(`📤 Resumen final enviado`);
    } catch (error) {
      console.log(`❌ Error enviando resumen: ${error.message}`);
    }
  }

  console.log(`\n🎉 PROCESO COMPLETADO: ${restoredCount} escudos restaurados`);
  return restoredCount;
}

// Función para mostrar el estado de escudos de todos los usuarios
function showAllShieldStatus() {
  console.log("🛡️ ESTADO DE ESCUDOS DE TODOS LOS USUARIOS");
  console.log("=" .repeat(60));

  const participants = getAllParticipants();
  let totalWithShields = 0;
  let totalEligibleForFirst = 0;
  let totalEligibleForRegen = 0;

  participants.forEach(userName => {
    const shieldStatus = getShieldStatus(userName);
    if (!shieldStatus) {
      console.log(`❌ ${userName}: No se pudo obtener estado`);
      return;
    }

    const hasUsedBefore = shieldStatus.shieldLastUsed !== null && shieldStatus.shieldLastUsed !== "";
    const streak = shieldStatus.currentStreak || 0;

    console.log(`\n👤 ${userName}:`);
    console.log(`   🔥 Racha actual: ${streak} días`);
    console.log(`   🛡️ Escudo disponible: ${shieldStatus.shieldAvailable ? 'SÍ' : 'NO'}`);
    console.log(`   📅 Ha usado escudo antes: ${hasUsedBefore ? 'SÍ' : 'NO'}`);

    if (shieldStatus.shieldAvailable) {
      console.log(`   ✅ Estado: PROTEGIDO`);
      totalWithShields++;
    } else {
      if (!hasUsedBefore) {
        const daysNeeded = Math.max(0, 7 - streak);
        console.log(`   🎯 Necesita ${daysNeeded} días más para primer escudo`);
        if (daysNeeded <= 2) totalEligibleForFirst++;
      } else {
        const daysNeeded = Math.max(0, 2 - streak);
        console.log(`   ⚡ Necesita ${daysNeeded} días más para regenerar escudo`);
        if (daysNeeded === 0) totalEligibleForRegen++;
      }
    }
  });

  console.log("\n" + "=" .repeat(60));
  console.log("📊 RESUMEN:");
  console.log(`🛡️ Usuarios con escudo: ${totalWithShields}`);
  console.log(`🎯 Cerca de primer escudo: ${totalEligibleForFirst}`);
  console.log(`⚡ Listos para regenerar: ${totalEligibleForRegen}`);
  console.log(`👥 Total usuarios: ${participants.length}`);

  return {
    totalUsers: participants.length,
    withShields: totalWithShields,
    eligibleForFirst: totalEligibleForFirst,
    eligibleForRegen: totalEligibleForRegen
  };
}

// Función para probar el fix del sistema de escudos
function testShieldDateFix() {
  console.log("🧪 PROBANDO FIX DEL SISTEMA DE ESCUDOS...");

  // Simular datos de prueba
  const testUsers = [
    { name: "TestUser1", lastDaily: new Date("2025-07-30T22:48:21") }, // 9.5 horas antes
    { name: "TestUser2", lastDaily: new Date("2025-07-30T20:40:29") }, // 11.7 horas antes
    { name: "TestUser3", lastDaily: new Date("2025-07-29T10:00:00") }  // 46.3 horas antes
  ];

  const checkTime = new Date("2025-07-31T08:20:00");
  console.log(`⏰ Hora de verificación: ${checkTime.toLocaleString()}`);
  console.log("");

  testUsers.forEach(user => {
    const timeDiff = checkTime - user.lastDaily;
    const hoursDiff = timeDiff / (1000 * 60 * 60);
    const hasRecent = hoursDiff < 24;

    console.log(`--- ${user.name} ---`);
    console.log(`📅 Última daily: ${user.lastDaily.toLocaleString()}`);
    console.log(`⏱️  Hace: ${hoursDiff.toFixed(1)} horas`);
    console.log(`✅ ¿Tiene daily reciente?: ${hasRecent}`);
    console.log(`🛡️ ¿Debería aplicar escudo?: ${!hasRecent}`);
    console.log("");
  });

  console.log("🎯 RESULTADO ESPERADO:");
  console.log("- TestUser1 y TestUser2: NO aplicar escudo (< 24h)");
  console.log("- TestUser3: SÍ aplicar escudo (> 24h)");

  return true;
}

// Función de testing del sistema de escudos estilo Duolingo
function testDuolingoShieldSystem() {
  console.log("🧪 PROBANDO SISTEMA DE ESCUDO ESTILO DUOLINGO...");

  const testUser = "TestUser_Duolingo";

  try {
    // 1. Crear usuario de prueba
    console.log("\n1️⃣ Creando usuario de prueba...");
    const gamif = getOrCreateSheet(GAMIF_SHEET_NAME, [
      "Nombre","Departamento","TotalDailies","Puntos","Streak","MaxStreak","UltimaFecha","Badge","Días sin Daily","Logros","ShieldAvailable","ShieldLock","ShieldLastUsed"
    ]);

    // Verificar si ya existe y eliminarlo
    const data = gamif.getDataRange().getValues();
    for (let i = data.length - 1; i >= 1; i--) {
      if (data[i][0] === testUser) {
        gamif.deleteRow(i + 1);
        break;
      }
    }

    // Crear usuario con racha 6 (casi 7)
    const yesterday = new Date();
    yesterday.setHours(yesterday.getHours() - 25);

    gamif.appendRow([
      testUser,           // Nombre
      "Test Dept",        // Departamento
      6,                  // TotalDailies
      20,                 // Puntos
      6,                  // Streak (casi 7)
      6,                  // MaxStreak
      yesterday.getTime(),// UltimaFecha (timestamp, hace 25 horas)
      "🔥",              // Badge
      1,                  // Días sin Daily
      "",                 // Logros
      false,              // ShieldAvailable (no tiene escudo aún)
      false,              // ShieldLock
      ""                  // ShieldLastUsed
    ]);

    console.log(`✅ Usuario ${testUser} creado con racha 6, sin escudo`);

    // 2. Simular daily que lleva a racha 7 (debería otorgar escudo)
    console.log("\n2️⃣ Simulando daily que alcanza racha 7...");
    const newStreak = 7;
    const userRowIndex = gamif.getDataRange().getValues().findIndex(r => r[0] === testUser) + 1;
    gamif.getRange(userRowIndex, 5).setValue(newStreak); // Actualizar streak a 7

    // Simular otorgamiento de escudo
    if (grantShieldOnStreak(testUser, newStreak)) {
      console.log("✅ Escudo otorgado automáticamente al alcanzar 7 días");
    } else {
      console.log("❌ Error al otorgar escudo");
    }

    // 3. Verificar estado del escudo
    console.log("\n3️⃣ Verificando estado del escudo...");
    const shieldStatus = getShieldStatus(testUser);
    console.log(`Estado: Streak=${shieldStatus.currentStreak}, Shield=${shieldStatus.shieldAvailable}`);

    // 4. Simular día sin daily (debería consumir escudo automáticamente)
    console.log("\n4️⃣ Simulando día sin daily (aplicación automática de escudo)...");
    if (shieldStatus.shieldAvailable) {
      if (consumeShield(testUser)) {
        console.log("✅ Escudo consumido automáticamente");

        const newShieldStatus = getShieldStatus(testUser);
        console.log(`Nuevo estado: Shield=${newShieldStatus.shieldAvailable}`);
      } else {
        console.log("❌ Error al consumir escudo");
      }
    }

    // 5. Probar regeneración rápida (2 días)
    console.log("\n5️⃣ Probando regeneración rápida (2 días)...");

    // Simular 2 dailies consecutivos
    for (let day = 1; day <= 2; day++) {
      const dailyDate = new Date();
      dailyDate.setDate(dailyDate.getDate() + day);

      // Actualizar streak y fecha
      gamif.getRange(userRowIndex, 5).setValue(day); // Streak 1, luego 2
      gamif.getRange(userRowIndex, 7).setValue(dailyDate.getTime()); // UltimaFecha

      console.log(`Daily ${day}: Streak ahora es ${day}`);

      // En el día 2, debería otorgar escudo nuevamente
      if (day === 2) {
        if (grantShieldOnStreak(testUser, day)) {
          console.log("✅ Escudo regenerado automáticamente después de 2 días");
        } else {
          console.log("❌ Error al regenerar escudo");
        }
      }
    }

    const finalStatus = getShieldStatus(testUser);
    console.log(`Estado final: Shield=${finalStatus.shieldAvailable}`);
    console.log("✅ Regeneración rápida funcionando: solo 2 días necesarios después del primer uso");

    // 6. Limpiar usuario de prueba
    console.log("\n6️⃣ Limpiando usuario de prueba...");
    const finalData = gamif.getDataRange().getValues();
    for (let i = finalData.length - 1; i >= 1; i--) {
      if (finalData[i][0] === testUser) {
        gamif.deleteRow(i + 1);
        console.log("✅ Usuario de prueba eliminado");
        break;
      }
    }

    console.log("\n🎉 Prueba del sistema estilo Duolingo completada exitosamente!");

    return {
      success: true,
      testUser: testUser,
      message: "Sistema de escudos estilo Duolingo funcionando correctamente"
    };

  } catch (error) {
    console.log(`❌ Error en prueba: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

// Función para probar el envío de dailies a Discord
function testDailyDiscordSending() {
  console.log("📤 PROBANDO ENVÍO DE DAILIES A DISCORD...");

  // Simular datos de daily
  const testDaily = {
    nombre: "TestUser_Discord",
    ayer: "Trabajé en el sistema de escudos y arreglé bugs",
    hoy: "Voy a probar el envío de dailies a Discord",
    imped: "Ninguno por ahora",
    timestamp: new Date()
  };

  console.log("📝 Simulando daily:");
  console.log(`   Usuario: ${testDaily.nombre}`);
  console.log(`   Ayer: ${testDaily.ayer}`);
  console.log(`   Hoy: ${testDaily.hoy}`);

  // Construir mensaje como en onFormSubmit
  const fechaStr = Utilities.formatDate(testDaily.timestamp, Session.getScriptTimeZone(), "dd/MM/yyyy");
  const displayName = testDaily.nombre; // Sin emojis para test

  let msg = `📆 **${fechaStr}** — **Daily de ${displayName}**\n` +
           `✅ **Ayer:** ${testDaily.ayer}\n` +
           `🛠️ **Hoy:** ${testDaily.hoy}\n` +
           `⛔ **Impedimentos:** ${testDaily.imped}`;

  msg += `\n\n🏅 **Racha:** 1 · Máx 1\n📈 **Total dailies:** 1\n⭐ **Puntos:** 1`;

  console.log("📤 Enviando mensaje de prueba a Discord...");
  console.log(`Mensaje: ${msg.substring(0, 100)}...`);

  try {
    UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
      method: "post",
      contentType: "application/json",
      payload: JSON.stringify({ content: msg })
    });

    console.log("✅ Mensaje de prueba enviado exitosamente");
    return true;
  } catch (error) {
    console.log(`❌ Error enviando mensaje: ${error.message}`);
    return false;
  }
}

// Función para probar el fix de las 24 horas
function test24HourFix() {
  console.log("🕐 PROBANDO FIX DE LAS 24 HORAS...");

  const now = new Date();
  const scenarios = [
    {
      name: "Daily hace 2 horas",
      lastDaily: new Date(now.getTime() - 2 * 60 * 60 * 1000),
      shouldApplyShield: false
    },
    {
      name: "Daily hace 12 horas",
      lastDaily: new Date(now.getTime() - 12 * 60 * 60 * 1000),
      shouldApplyShield: false
    },
    {
      name: "Daily hace 23 horas",
      lastDaily: new Date(now.getTime() - 23 * 60 * 60 * 1000),
      shouldApplyShield: false
    },
    {
      name: "Daily hace 25 horas",
      lastDaily: new Date(now.getTime() - 25 * 60 * 60 * 1000),
      shouldApplyShield: true
    },
    {
      name: "Daily hace 30 horas",
      lastDaily: new Date(now.getTime() - 30 * 60 * 60 * 1000),
      shouldApplyShield: true
    }
  ];

  console.log(`⏰ Hora actual: ${now.toLocaleString()}`);
  console.log("");

  scenarios.forEach(scenario => {
    const timeDiff = (now - scenario.lastDaily) / (1000 * 60 * 60);
    const isRecent = timeDiff < 24;
    const shouldApplyShield = !isRecent; // Lógica del sistema

    console.log(`--- ${scenario.name} ---`);
    console.log(`📅 Última daily: ${scenario.lastDaily.toLocaleString()}`);
    console.log(`⏱️  Hace: ${timeDiff.toFixed(1)} horas`);
    console.log(`✅ ¿Es reciente? (< 24h): ${isRecent}`);
    console.log(`🛡️ ¿Aplicar escudo?: ${shouldApplyShield}`);
    console.log(`🎯 ¿Correcto?: ${shouldApplyShield === scenario.shouldApplyShield ? '✅ SÍ' : '❌ NO'}`);
    console.log("");
  });

  console.log("🎯 RESULTADO:");
  console.log("- Dailies < 24h: NO aplicar escudo (usuario está activo)");
  console.log("- Dailies > 24h: SÍ aplicar escudo (usuario necesita protección)");

  return true;
}

/* ==========================================================
 * 10) SISTEMA DE MOTIVACIÓN DEL PROYECTO
 * ========================================================== */

// Generar estadísticas del proyecto para motivar al equipo
function generateProjectStats() {
  console.log("📊 GENERANDO ESTADÍSTICAS DEL PROYECTO...");

  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const resp = ss.getSheetByName("Respuestas de formulario 1");
  if (!resp) return null;

  const data = resp.getDataRange().getValues();
  const header = data[0];
  const idxName = header.indexOf(NAME_FIELD);
  const idxTime = header.indexOf("Marca temporal");
  const idxHoy = header.indexOf("¿Qué vas a hacer hoy? (tareas o trabajo planeado)");
  const idxAyer = header.indexOf("¿Qué hiciste ayer? (tareas finalizadas o avances)");
  const idxImped = header.indexOf("¿Tienes algún impedimento? (detalle lo que te está deteniendo, si hay algo)");

  const today = new Date();
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

  const stats = {
    totalDailies: 0,
    projectDailies: 0,
    highFocusDailies: 0,
    userStats: {},
    topContributors: [],
    projectAchievements: 0
  };

  // Procesar todas las dailies
  for (let r = 1; r < data.length; r++) {
    const row = data[r];
    const name = String(row[idxName] || "").trim();
    const raw = row[idxTime];
    const hoy = String(row[idxHoy] || "").trim();
    const ayer = String(row[idxAyer] || "").trim();
    const imped = String(row[idxImped] || "").trim();

    if (!name || !hoy) continue;

    const dObj = raw instanceof Date ? raw : parseDateStr(String(raw));
    if (!dObj || dObj < weekAgo) continue; // Solo última semana

    stats.totalDailies++;

    const dailyData = { timestamp: dObj, ayer, hoy, imped, nombre: name };
    const isProjectWork = detectProjectWork(dailyData);
    const projectFocus = calculateProjectFocus(dailyData);

    if (isProjectWork) {
      stats.projectDailies++;

      if (!stats.userStats[name]) {
        stats.userStats[name] = { projectDailies: 0, totalDailies: 0, highFocus: 0 };
      }
      stats.userStats[name].projectDailies++;
    }

    if (projectFocus >= 0.8) {
      stats.highFocusDailies++;
      if (stats.userStats[name]) {
        stats.userStats[name].highFocus++;
      }
    }

    if (stats.userStats[name]) {
      stats.userStats[name].totalDailies++;
    }
  }

  // Calcular top contributors
  stats.topContributors = Object.entries(stats.userStats)
    .sort(([,a], [,b]) => b.projectDailies - a.projectDailies)
    .slice(0, 5)
    .map(([name, data]) => ({ name, ...data }));

  // Contar logros del proyecto obtenidos
  const achievementSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName("UserAchievements");
  if (achievementSheet) {
    const achData = achievementSheet.getDataRange().getValues();
    for (let i = 1; i < achData.length; i++) {
      const [, achievementId, date] = achData[i];
      const achievement = UNIQUE_ACHIEVEMENTS.find(ach => ach.id === achievementId);
      if (achievement && achievement.category === "Proyecto") {
        const achDate = date instanceof Date ? date : new Date(date);
        if (achDate >= weekAgo) {
          stats.projectAchievements++;
        }
      }
    }
  }

  return stats;
}

// Enviar estadísticas motivacionales del proyecto a Discord
function sendProjectMotivationToDiscord() {
  const stats = generateProjectStats();
  if (!stats) {
    console.log("❌ No se pudieron generar estadísticas del proyecto");
    return;
  }

  const projectPercentage = stats.totalDailies > 0 ? Math.round((stats.projectDailies / stats.totalDailies) * 100) : 0;
  const focusPercentage = stats.projectDailies > 0 ? Math.round((stats.highFocusDailies / stats.projectDailies) * 100) : 0;

  let msg = `🚀 **REPORTE SEMANAL DEL PROYECTO** 📊\n\n`;

  msg += `📈 **Estadísticas Generales:**\n`;
  msg += `• Total dailies esta semana: **${stats.totalDailies}**\n`;
  msg += `• Dailies sobre el proyecto: **${stats.projectDailies}** (${projectPercentage}%)\n`;
  msg += `• Dailies con alto enfoque: **${stats.highFocusDailies}** (${focusPercentage}%)\n`;
  msg += `• Logros del proyecto desbloqueados: **${stats.projectAchievements}** 🏆\n\n`;

  if (stats.topContributors.length > 0) {
    msg += `🏆 **TOP CONTRIBUIDORES AL PROYECTO:**\n`;
    stats.topContributors.forEach((user, index) => {
      const medal = index === 0 ? "🥇" : index === 1 ? "🥈" : index === 2 ? "🥉" : `${index + 1}.`;
      const percentage = user.totalDailies > 0 ? Math.round((user.projectDailies / user.totalDailies) * 100) : 0;
      msg += `${medal} **${user.name}** - ${user.projectDailies} dailies del proyecto (${percentage}%)\n`;
    });
    msg += `\n`;
  }

  // Mensajes motivacionales basados en el rendimiento
  if (projectPercentage >= 80) {
    msg += `🎉 **¡EXCELENTE!** El equipo está súper enfocado en el proyecto!\n`;
  } else if (projectPercentage >= 60) {
    msg += `👏 **¡Buen trabajo!** El equipo mantiene buen enfoque en el proyecto.\n`;
  } else if (projectPercentage >= 40) {
    msg += `💪 **¡Vamos por más!** Podemos aumentar el enfoque en el proyecto.\n`;
  } else {
    msg += `🎯 **¡Oportunidad de mejora!** ¡Reportemos más trabajo del proyecto!\n`;
  }

  msg += `\n💡 **Tip:** Menciona palabras como "desarrollo", "implementé", "fix", "testing" para obtener bonos del proyecto!`;

  UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
    method: "post",
    contentType: "application/json",
    payload: JSON.stringify({ content: msg })
  });

  console.log("✅ Reporte de motivación del proyecto enviado a Discord");
  return stats;
}

// Calcular estadísticas avanzadas del usuario para evaluación de logros
function calculateUserStats(userName) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const resp = ss.getSheetByName("Respuestas de formulario 1");
  const gamif = ss.getSheetByName(GAMIF_SHEET_NAME);

  if (!resp || !gamif) return null;

  // Obtener datos básicos de gamificación
  const gamifData = gamif.getDataRange().getValues();
  let userRow = null;
  for (let i = 1; i < gamifData.length; i++) {
    if (gamifData[i][0] === userName) {
      userRow = gamifData[i];
      break;
    }
  }

  if (!userRow) return null;

  const [, dept, totalDailies, , currentStreak, maxStreak] = userRow;

  // Obtener todas las dailies del usuario
  const data = resp.getDataRange().getValues();
  const header = data[0];
  const idxName = header.indexOf(NAME_FIELD);
  const idxTime = header.indexOf("Marca temporal");
  const idxHoy = header.indexOf("¿Qué vas a hacer hoy? (tareas o trabajo planeado)");
  const idxAyer = header.indexOf("¿Qué hiciste ayer? (tareas finalizadas o avances)");
  const idxImped = header.indexOf("¿Tienes algún impedimento? (detalle lo que te está deteniendo, si hay algo)");

  const userDailies = [];
  const daysOfWeek = new Set();
  let luckyDaysHit = 0;
  let wordOfDayUsed = 0;
  let longestDaily = 0;
  const hourCounts = {};

  const { luckyDateStr, wordOfDay } = getConfigVars();

  for (let r = 1; r < data.length; r++) {
    const row = data[r];
    if (String(row[idxName] || "").trim() === userName) {
      const raw = row[idxTime];
      const dObj = raw instanceof Date ? raw : parseDateStr(String(raw));
      if (dObj) {
        const ayer = String(row[idxAyer] || "");
        const hoy = String(row[idxHoy] || "");
        const imped = String(row[idxImped] || "");

        userDailies.push({ date: dObj, ayer, hoy, imped });
        daysOfWeek.add(dObj.getDay());

        // Contar Lucky Days
        const dateStr = Utilities.formatDate(dObj, Session.getScriptTimeZone(), "dd/MM/yyyy");
        if (dateStr === luckyDateStr) luckyDaysHit++;

        // Contar uso de palabra del día
        if (wordOfDay && new RegExp(`\\b${wordOfDay}\\b`, "i").test(ayer + " " + hoy + " " + imped)) {
          wordOfDayUsed++;
        }

        // Daily más largo
        const dailyLength = (ayer + " " + hoy + " " + imped).length;
        longestDaily = Math.max(longestDaily, dailyLength);

        // Contar horas
        const hour = dObj.getHours();
        hourCounts[hour] = (hourCounts[hour] || 0) + 1;
      }
    }
  }

  // Calcular semanas perfectas (7 dailies en 7 días diferentes)
  let perfectWeeks = 0;
  // Simplificado: si tiene dailies en todos los días de la semana, cuenta como 1
  if (daysOfWeek.size === 7) perfectWeeks = 1;

  // Calcular racha de misma hora
  let sameHourStreak = 0;
  const maxHourCount = Math.max(...Object.values(hourCounts));
  if (maxHourCount >= 5) sameHourStreak = maxHourCount;

  // Calcular estadísticas del departamento
  let departmentRank = 1;
  let departmentAverage = totalDailies;
  let departmentTotal = 0;
  let departmentCount = 0;

  for (let i = 1; i < gamifData.length; i++) {
    const [, otherDept, otherTotal] = gamifData[i];
    if (otherDept === dept) {
      departmentTotal += otherTotal || 0;
      departmentCount++;
      if ((otherTotal || 0) > totalDailies) {
        departmentRank++;
      }
    }
  }

  if (departmentCount > 0) {
    departmentAverage = departmentTotal / departmentCount;
  }

  return {
    totalDailies,
    currentStreak,
    maxStreak,
    daysOfWeekCompleted: daysOfWeek.size,
    luckyDaysHit,
    wordOfDayUsed,
    perfectWeeks,
    sameHourStreak,
    departmentRank,
    departmentAverage,
    monthlyLongestDaily: false, // Se calculará mensualmente
    longestDailyLength: longestDaily
  };
}

function getHistoricalMap() {
  const src = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SOURCE_SHEET_NAME);
  const map = {};
  if (!src) return map;
  let dept="";
  src.getDataRange().getValues().forEach(([n,v])=>{
    if(v===""||v===null) dept=String(n).trim();
    else if(n) map[String(n).trim()]={dept};
  });
  return map;
}

function parseDateStr(str){
  if(!str||typeof str!=="string") return null;
  let dObj=new Date(str);
  if(!isNaN(dObj)) return dObj;
  const m=str.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})(?:\s+(\d{2}:\d{2}:\d{2}))?/);
  if(!m) return null;
  const [,d,mo,y,time="00:00:00"]=m;
  dObj=new Date(`${y}-${(`0${mo}`).slice(-2)}-${(`0${d}`).slice(-2)} ${time}`);
  return isNaN(dObj)?null:dObj;
}

// ------------- CONFIG SHEET VARS -------------
function getOrCreateConfigSheet(){
  return getOrCreateSheet(CONFIG_SHEET_NAME,["LuckyDate (dd/MM/yyyy)","WordOfDay","WordPool"]);
}

function getConfigVars(){
  const cfg=getOrCreateConfigSheet();
  return {
    luckyDateStr:String(cfg.getRange("A1").getValue()||"").trim(),
    wordOfDay:String(cfg.getRange("B1").getValue()||"").trim()
  };
}

/* ==========================================================
 * 1) ON‑FORM‑SUBMIT  →  actualiza fila + envía Discord
 * ========================================================== */
function onFormSubmit(e){
  // DEBUG: Log inicio de procesamiento
  console.log("🚀 INICIANDO PROCESAMIENTO DE DAILY");

  const ayer=e.namedValues["¿Qué hiciste ayer? (tareas finalizadas o avances)"]?.[0]?.trim()||"";
  const hoy =e.namedValues["¿Qué vas a hacer hoy? (tareas o trabajo planeado)"]?.[0]?.trim()||"";
  const imped=e.namedValues["¿Tienes algún impedimento? (detalle lo que te está deteniendo, si hay algo)"]?.[0]?.trim()||"";
  const marca=e.namedValues["Marca temporal"]?.[0]?.trim()||"";
  const nombre=e.namedValues[NAME_FIELD]?.[0]?.trim()||"Usuario sin nombre";

  console.log(`📝 Daily de: ${nombre}`);
  console.log(`📅 Timestamp: ${marca}`);
  console.log(`✅ Hoy: ${hoy.substring(0, 50)}...`);

  if(!hoy) {
    console.log("❌ Daily vacía, cancelando procesamiento");
    return; // daily vacía
  }

  // FIXED: Parsing más robusto del timestamp del formulario
  let dateObj = null;

  // Intentar diferentes métodos de parsing
  if (marca) {
    console.log(`🔍 Intentando parsear timestamp: "${marca}"`);

    // Método 1: Crear Date directamente (más robusto)
    dateObj = new Date(marca);

    // Método 2: Si falla, usar parseDateStr
    if (!dateObj || isNaN(dateObj.getTime())) {
      console.log(`⚠️ Método 1 falló, intentando parseDateStr...`);
      dateObj = parseDateStr(marca);
    }

    // Método 3: Si todo falla, usar fecha actual
    if (!dateObj || isNaN(dateObj.getTime())) {
      console.log(`⚠️ Todos los métodos fallaron, usando fecha actual`);
      dateObj = new Date();
    }

    console.log(`✅ Fecha final: ${dateObj.toLocaleString()}`);
  } else {
    console.log(`❌ Sin timestamp, usando fecha actual`);
    dateObj = new Date();
  }

  if (!dateObj || isNaN(dateObj.getTime())) {
    console.log(`❌ Error crítico: No se pudo crear fecha válida`);
    return;
  }
  const fechaStr=Utilities.formatDate(dateObj,Session.getScriptTimeZone(),"dd/MM/yyyy");

  const {luckyDateStr,wordOfDay}=getConfigVars();
  const gameConfig = getGameConfig();
  const isLuckyDay = luckyDateStr===fechaStr;
  const usedWord   = wordOfDay && new RegExp(`\\b${wordOfDay}\\b`,`i`).test(`${ayer} ${hoy}`);

  // --- hoja Gamification ---------
  const gamif=getOrCreateSheet(GAMIF_SHEET_NAME,["Nombre","Departamento","TotalDailies","Puntos","Streak","MaxStreak","UltimaFecha","Badge","Días sin Daily","Logros","ShieldAvailable","ShieldLock","ShieldLastUsed"]);
  const hist=getHistoricalMap();
  let rowIdx=gamif.getDataRange().getValues().findIndex(r=>r[0]===nombre);
  if(rowIdx===-1){
    const dept=hist[nombre]?.dept||"";
    gamif.appendRow([nombre,dept,0,0,0,0,"","","","",true,false,""]);
    rowIdx=gamif.getLastRow()-1;
  }
  rowIdx+=1; // 1‑based
  const COL={TOTAL:3,POINTS:4,STREAK:5,MAX:6,DATE:7,BADGE:8,DAYS_WITHOUT:9,ACHIEVEMENTS:10,SHIELD_AVAILABLE:11,SHIELD_LOCK:12,SHIELD_LAST_USED:13};

  // Leer datos actuales en batch
  const currentData = gamif.getRange(rowIdx, COL.TOTAL, 1, 7).getValues()[0];
  let [total, puntos, streak, maxSt, rawLastDate] = currentData;
  total = total || 0;
  puntos = puntos || 0;
  streak = streak || 0;
  maxSt = maxSt || 0;

  // FIXED: Convertir timestamp a Date si es necesario
  let lastDate = rawLastDate;
  if (lastDate && typeof lastDate === 'number') {
    lastDate = new Date(lastDate);
  } else if (lastDate && typeof lastDate === 'string') {
    lastDate = parseDateStr(lastDate);
  }

  // FIXED: Cambiar de "mismo día" a "últimas 24 horas" para evitar problemas con escudos
  const timeDiff = lastDate ? (dateObj - lastDate) / (1000 * 60 * 60) : 999; // horas de diferencia
  const isRecentDaily = timeDiff < 24; // Menos de 24 horas = daily reciente

  console.log(`🔍 ${nombre}: Última daily hace ${timeDiff.toFixed(1)} horas, ¿Es reciente? ${isRecentDaily}`);

  let earned = 0;
  let achievementsEarned = [];

  // Puntos base y streak
  if(!isRecentDaily){ // Si NO es daily reciente (>24h), procesar como nueva
    total += 1;
    earned += gameConfig.PuntoBase || 1;

    // Lógica mejorada de streak que preserva rachas existentes
    if (lastDate instanceof Date) {
      const daysDiff = Math.floor((dateObj - lastDate) / 864e5);
      if (daysDiff === 1) {
        // Día consecutivo: continuar streak
        streak = streak + 1;
      } else if (daysDiff > 1) {
        // Se rompió la racha: empezar nueva
        streak = 1;
      }
      // Si daysDiff === 0, es daily muy reciente (< 24h pero mismo día)
      // Si daysDiff < 0, es una fecha anterior (caso raro, mantener streak actual)
    } else {
      // Primera vez o sin fecha previa: empezar streak
      streak = 1;
    }

    maxSt = Math.max(maxSt, streak);
  }

  // NUEVO: Sistema de escudo automático estilo Duolingo (otorgar escudo al alcanzar 7 días)
  if (!isRecentDaily) { // Solo verificar en nuevos dailies (no recientes)
    grantShieldOnStreak(nombre, streak);
  }

  // Bonificaciones tradicionales
  if(usedWord && gameConfig.BonusWord) earned += gameConfig.BonusWord;
  if(isLuckyDay && gameConfig.BonusLucky) earned += gameConfig.BonusLucky;

  // Crear objeto dailyData para evaluaciones
  const dailyData = { timestamp: dateObj, ayer, hoy, imped, nombre };

  // NUEVO: Bonificaciones por trabajo del proyecto
  const isProjectWork = detectProjectWork(dailyData);
  const projectFocusLevel = calculateProjectFocus(dailyData);

  if(isProjectWork && gameConfig.BonusProjectWork) {
    earned += gameConfig.BonusProjectWork;
  }

  // Multiplicador si la daily está muy enfocada en el proyecto
  if(projectFocusLevel >= 0.8 && gameConfig.MultiplierProjectFocus) {
    earned = Math.floor(earned * gameConfig.MultiplierProjectFocus);
  }

  // Evaluar logros únicos
  const userStats = calculateUserStats(nombre);

  if (userStats) {
    // Actualizar estadísticas con el daily actual
    userStats.totalDailies = total; // Usar el total actualizado
    userStats.currentStreak = streak; // Usar el streak actualizado

    UNIQUE_ACHIEVEMENTS.forEach(achievement => {
      // Solo evaluar si el usuario no tiene ya este logro
      if (!hasUserAchievement(nombre, achievement.id)) {
        if (achievement.condition(dailyData, userStats)) {
          if (grantAchievement(nombre, achievement.id)) {
            earned += achievement.points;
            achievementsEarned.push(achievement);
          }
        }
      }
    });
  }

  // Aplicar multiplicadores
  if (gameConfig.MultiplierWeekend) {
    const isWeekend = dateObj.getDay() === 0 || dateObj.getDay() === 6;
    if (isWeekend) earned = Math.floor(earned * gameConfig.MultiplierWeekend);
  }

  puntos += earned;

  // ELIMINADO: Lógica antigua de restauración de escudos
  // Ahora los escudos se otorgan automáticamente al alcanzar 7 días de racha

  // Actualizar datos en batch
  if (!isRecentDaily) { // Si NO es daily reciente, actualizar todo
    gamif.getRange(rowIdx, COL.TOTAL, 1, 4).setValues([[total, puntos, streak, maxSt]]);
    // FIXED: Guardar fecha con hora completa como timestamp para evitar pérdida de precisión
    gamif.getRange(rowIdx, COL.DATE).setValue(dateObj.getTime());
  } else {
    // Si es daily reciente (< 24h), solo actualizar puntos (daily duplicada/actualizada)
    gamif.getRange(rowIdx, COL.POINTS).setValue(puntos);
  }

  // Badge y extras
  let badge="";
  for(let i=BADGE_MILESTONES.length-1;i>=0;i--){
    if(streak>=BADGE_MILESTONES[i].days){
      badge=BADGE_MILESTONES[i].emoji;
      break;
    }
  }

  let extra="";
  if(isLuckyDay) extra+=" 🍀";
  if(usedWord) extra+=" 💡";
  achievementsEarned.forEach(ach => extra += " " + ach.emoji);

  gamif.getRange(rowIdx,COL.BADGE).setValue((badge+extra).trim());

  // Actualizar logros obtenidos
  if (achievementsEarned.length > 0) {
    const currentAchievements = gamif.getRange(rowIdx, COL.ACHIEVEMENTS).getValue() || "";
    const newAchievements = achievementsEarned.map(a => a.name).join(", ");
    const updatedAchievements = currentAchievements ?
      currentAchievements + ", " + newAchievements : newAchievements;
    gamif.getRange(rowIdx, COL.ACHIEVEMENTS).setValue(updatedAchievements);
  }

  // --- Discord mensaje mejorado con logros únicos ---------
  const displayName = createDisplayName(nombre, badge, extra);

  let msg=`📆 **${fechaStr}** — **Daily de ${displayName}**\n`+
          `✅ **Ayer:** ${ayer||"No especificado"}\n`+
          `🛠️ **Hoy:** ${hoy}\n`+
          `⛔ **Impedimentos:** ${imped||"Ninguno"}`;

  if(isLuckyDay) msg+="\n🍀 **Lucky Day!** +" + (gameConfig.BonusLucky || 1) + " punto extra.";
  if(usedWord) msg+=`\n💡 Palabra *${wordOfDay}* usada (+${gameConfig.BonusWord || 1} punto).`;

  // ELIMINADO: Lógica antigua de restauración de escudo
  // Los escudos ahora se otorgan automáticamente en grantShieldOnStreak()

  // NUEVO: Mostrar bonificaciones del proyecto
  if(isProjectWork) msg+=`\n⚔️ **¡Trabajo del proyecto detectado!** (+${gameConfig.BonusProjectWork || 3} pts)`;
  if(projectFocusLevel >= 0.8) msg+=`\n🎯 **Daily enfocada en proyecto** (x${gameConfig.MultiplierProjectFocus || 1.5} multiplicador)`;
  if(projectFocusLevel >= 0.5 && projectFocusLevel < 0.8) msg+=`\n📊 **Buen enfoque en proyecto** (${Math.round(projectFocusLevel * 100)}% enfoque)`;

  // Obtener TODOS los logros pendientes de notificar (incluye los obtenidos hoy y anteriores)
  const pendingNotifications = getPendingNotifications(nombre);

  if(pendingNotifications.length > 0) {
    msg += "\n\n🎉 **¡LOGROS ÚNICOS DESBLOQUEADOS!**";

    // Separar logros nuevos (de hoy) de logros anteriores pendientes
    const newAchievements = [];
    const previousAchievements = [];

    pendingNotifications.forEach(ach => {
      const today = Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "dd/MM/yyyy");
      const achievementDate = Utilities.formatDate(ach.dateEarned, Session.getScriptTimeZone(), "dd/MM/yyyy");

      if (achievementDate === today) {
        newAchievements.push(ach);
      } else {
        previousAchievements.push(ach);
      }
    });

    // Mostrar logros nuevos primero
    if (newAchievements.length > 0) {
      msg += "\n\n✨ **¡Logros obtenidos HOY!**";
      newAchievements.forEach(ach => {
        msg += `\n${ach.emoji} **${ach.name}** - ${ach.description} (+${ach.points} pts)`;
      });
    }

    // Mostrar logros anteriores pendientes
    if (previousAchievements.length > 0) {
      msg += "\n\n🏆 **¡Logros anteriores sin notificar!**";
      previousAchievements.forEach(ach => {
        const dateStr = Utilities.formatDate(ach.dateEarned, Session.getScriptTimeZone(), "dd/MM/yyyy");
        msg += `\n${ach.emoji} **${ach.name}** - ${ach.description} (${dateStr}) (+${ach.points} pts)`;
      });
    }

    // Marcar todos como notificados
    const achievementIds = pendingNotifications.map(ach => ach.id);
    markAchievementsAsNotified(nombre, achievementIds);
  }

  msg+=`\n\n🏅 **Racha:** ${streak} · Máx ${maxSt}\n📈 **Total dailies:** ${total}\n⭐ **Puntos:** ${puntos}`;
  if(earned > 1) msg += ` (+${earned} pts hoy)`;

  // DEBUG: Log antes de enviar daily a Discord
  console.log("📤 ENVIANDO DAILY A DISCORD:");
  console.log(`Usuario: ${nombre}`);
  console.log(`Mensaje: ${msg.substring(0, 200)}...`);

  try {
    UrlFetchApp.fetch(DISCORD_WEBHOOK_URL,{method:"post",contentType:"application/json",payload:JSON.stringify({content:msg})});
    console.log("✅ Daily enviada exitosamente a Discord");
  } catch (error) {
    console.log(`❌ Error enviando daily a Discord: ${error.message}`);
  }
}

/* ==========================================================
 * 2) WORD OF DAY  &  LUCKY DAY
 * ========================================================== */
function getWordPool(){
  const cfg=getOrCreateConfigSheet();
  const col=cfg.getRange(2,3,cfg.getLastRow()-1).getValues().flat();
  return col.filter(w=>w&&String(w).trim()!="");
}

function setWordOfDay(){
  const pool=getWordPool();
  const word=pool[Math.floor(Math.random()*pool.length)]||"pingüino";
  const cfg=getOrCreateConfigSheet();
  cfg.getRange("B1").setValue(word);

  const lucky=cfg.getRange("A1").getValue();
  const today=Utilities.formatDate(new Date(),Session.getScriptTimeZone(),"dd/MM/yyyy");
  const msg=`💡 **Nueva palabra del día:** *${word}*` + (lucky===today?"\n🍀 **Hoy también es Lucky Day!**":"");
  UrlFetchApp.fetch(DISCORD_WEBHOOK_URL,{method:"post",contentType:"application/json",payload:JSON.stringify({content:msg})});
}

function setWeeklyLuckyDay(){
  const today=new Date();
  const nextDow=new Date(today.getFullYear(),today.getMonth(),today.getDate()+Math.floor(Math.random()*7));
  const fechaStr=Utilities.formatDate(nextDow,Session.getScriptTimeZone(),"dd/MM/yyyy");
  getOrCreateConfigSheet().getRange("A1").setValue(fechaStr);
  UrlFetchApp.fetch(DISCORD_WEBHOOK_URL,{method:"post",contentType:"application/json",payload:JSON.stringify({content:`🍀 **Nuevo Lucky Day!** Será el ${fechaStr}.`})});
}

/* ==========================================================
 * 3) FUNCIONES DE ACTUALIZACIÓN DE STREAKS
 * ========================================================== */

// Actualiza streaks preservando los datos existentes (recomendado para uso regular)
function updateStreaksPreservingData() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const resp = ss.getSheetByName("Respuestas de formulario 1");
  const gamif = ss.getSheetByName(GAMIF_SHEET_NAME);

  if (!resp || !gamif) {
    console.log("❌ Faltan hojas necesarias");
    return;
  }

  console.log("🔄 Actualizando streaks preservando datos existentes...");

  const today = new Date();
  const data = resp.getDataRange().getValues();
  const header = data[0];
  const idxName = header.indexOf(NAME_FIELD);
  const idxTime = header.indexOf("Marca temporal");
  const idxHoy = header.indexOf("¿Qué vas a hacer hoy? (tareas o trabajo planeado)");

  if ([idxName, idxTime, idxHoy].some(i => i === -1)) {
    console.log("❌ Columnas requeridas no encontradas");
    return;
  }

  // Obtener datos actuales de gamificación
  const gamifData = gamif.getDataRange().getValues();
  const COL = {
    NAME: 0, DEPT: 1, TOTAL: 2, POINTS: 3, STREAK: 4, MAX: 5, DATE: 6, BADGE: 7, DAYS_WITHOUT: 8, ACHIEVEMENTS: 9
  };

  // Procesar cada participante existente
  for (let i = 1; i < gamifData.length; i++) {
    const currentRow = gamifData[i];
    const name = String(currentRow[COL.NAME] || "").trim();
    if (!name) continue;

    // Obtener todas las dailies de este participante, ordenadas por fecha
    const userDailies = [];
    for (let r = 1; r < data.length; r++) {
      const row = data[r];
      const rowName = String(row[idxName] || "").trim();
      const raw = row[idxTime];
      const hoyTxt = String(row[idxHoy] || "").trim();

      if (rowName === name && hoyTxt) {
        const dObj = raw instanceof Date ? raw : parseDateStr(String(raw));
        if (dObj) {
          userDailies.push(dObj);
        }
      }
    }

    if (userDailies.length === 0) continue;

    // Ordenar por fecha
    userDailies.sort((a, b) => a - b);

    // Calcular streak actual y máximo histórico
    let currentStreak = 0;
    let maxStreakEver = currentRow[COL.MAX] || 0; // Preservar máximo existente

    // Calcular streak desde la fecha más reciente hacia atrás
    const sortedDesc = [...userDailies].sort((a, b) => b - a);
    const mostRecent = sortedDesc[0];

    // Verificar si el streak actual sigue activo
    const daysSinceLastDaily = Math.floor((today - mostRecent) / 864e5);

    if (daysSinceLastDaily <= 1) { // Streak activo (hoy o ayer)
      // Calcular streak actual contando hacia atrás desde la fecha más reciente
      currentStreak = 1;
      let checkDate = new Date(mostRecent);

      for (let j = 1; j < sortedDesc.length; j++) {
        const prevDate = sortedDesc[j];
        const expectedPrevDate = new Date(checkDate);
        expectedPrevDate.setDate(expectedPrevDate.getDate() - 1);

        // Comparar solo las fechas (sin hora)
        const prevDateStr = Utilities.formatDate(prevDate, Session.getScriptTimeZone(), "yyyy-MM-dd");
        const expectedDateStr = Utilities.formatDate(expectedPrevDate, Session.getScriptTimeZone(), "yyyy-MM-dd");

        if (prevDateStr === expectedDateStr) {
          currentStreak++;
          checkDate = prevDate;
        } else {
          break; // Se rompió la secuencia
        }
      }
    } else {
      currentStreak = 0; // Streak roto
    }

    // Calcular máximo histórico recorriendo todas las secuencias
    let tempStreak = 0;
    let prevDate = null;

    userDailies.forEach(dailyDate => {
      if (prevDate && Math.floor((dailyDate - prevDate) / 864e5) === 1) {
        tempStreak++;
      } else {
        tempStreak = 1;
      }
      maxStreakEver = Math.max(maxStreakEver, tempStreak);
      prevDate = dailyDate;
    });

    // Actualizar solo si hay cambios significativos
    const oldStreak = currentRow[COL.STREAK] || 0;
    const oldMaxStreak = currentRow[COL.MAX] || 0;

    if (currentStreak !== oldStreak || maxStreakEver !== oldMaxStreak) {
      console.log(`📊 ${name}: Streak ${oldStreak}→${currentStreak}, Max ${oldMaxStreak}→${maxStreakEver}`);

      // Actualizar streak y max streak
      gamif.getRange(i + 1, COL.STREAK + 1).setValue(currentStreak);
      gamif.getRange(i + 1, COL.MAX + 1).setValue(maxStreakEver);

      // Actualizar badge basado en el nuevo streak
      let badge = "";
      for (let b = BADGE_MILESTONES.length - 1; b >= 0; b--) {
        if (currentStreak >= BADGE_MILESTONES[b].days) {
          badge = BADGE_MILESTONES[b].emoji;
          break;
        }
      }

      // Preservar extras existentes en el badge
      const currentBadge = String(currentRow[COL.BADGE] || "");
      const extras = currentBadge.replace(/[🔥🏅🌟🚀🏆]/g, "").trim();
      const newBadge = (badge + " " + extras).trim();

      gamif.getRange(i + 1, COL.BADGE + 1).setValue(newBadge);

      // Actualizar días sin daily
      const daysWithout = daysSinceLastDaily > 1 ? daysSinceLastDaily : "";
      gamif.getRange(i + 1, COL.DAYS_WITHOUT + 1).setValue(daysWithout);
    }
  }

  console.log("✅ Actualización de streaks completada");
}

// Función para verificar y mostrar el estado de streaks de todos los participantes
function verifyAllStreaks() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const gamif = ss.getSheetByName(GAMIF_SHEET_NAME);

  if (!gamif) {
    console.log("❌ Hoja de gamificación no encontrada");
    return;
  }

  const data = gamif.getDataRange().getValues();
  const today = new Date();

  console.log("📊 ESTADO ACTUAL DE STREAKS:");
  console.log("=" .repeat(50));

  for (let i = 1; i < data.length; i++) {
    const [name, , totalDailies, , streak, maxStreak, rawLastDate] = data[i];

    if (!name) continue;

    // FIXED: Convertir timestamp a Date si es necesario
    let lastDate = rawLastDate;
    if (lastDate && typeof lastDate === 'number') {
      lastDate = new Date(lastDate);
    } else if (lastDate && typeof lastDate === 'string') {
      lastDate = parseDateStr(lastDate);
    }

    const daysSinceLastDaily = lastDate ? Math.floor((today - lastDate) / 864e5) : "N/A";
    const streakStatus = daysSinceLastDaily <= 1 ? "🔥 ACTIVO" : "💤 INACTIVO";

    console.log(`👤 ${name}:`);
    console.log(`   Streak actual: ${streak} días ${streakStatus}`);
    console.log(`   Máximo histórico: ${maxStreak} días`);
    console.log(`   Total dailies: ${totalDailies}`);
    console.log(`   Días sin daily: ${daysSinceLastDaily}`);
    console.log(`   Última fecha: ${lastDate ? Utilities.formatDate(lastDate, Session.getScriptTimeZone(), "dd/MM/yyyy") : "N/A"}`);
    console.log("");
  }

  return data.slice(1).map(row => ({
    name: row[0],
    streak: row[4],
    maxStreak: row[5],
    lastDate: row[6],
    daysSince: row[6] ? Math.floor((today - row[6]) / 864e5) : null
  }));
}

/* ==========================================================
 * 4) REBUILD COMPLETO (para auditoría manual)
 * ========================================================== */
function rebuildGamificationTable() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const resp = ss.getSheetByName("Respuestas de formulario 1");
  if (!resp) throw new Error("Falta hoja de respuestas");

  const gamif = getOrCreateSheet(
    GAMIF_SHEET_NAME,
    ["Nombre", "Departamento", "TotalDailies", "Puntos", "Streak", "MaxStreak", "UltimaFecha", "Badge", "Días sin Daily", "Logros", "ShieldAvailable", "ShieldLock", "ShieldLastUsed"]
  );
  const deptMap = getHistoricalMap();
  const { luckyDateStr, wordOfDay } = getConfigVars();
  const gameConfig = getGameConfig();
  const today = new Date();

  const data = resp.getDataRange().getValues();
  const header = data[0];
  const idxName = header.indexOf(NAME_FIELD);
  const idxTime = header.indexOf("Marca temporal");
  const idxAyer = header.indexOf("¿Qué hiciste ayer? (tareas finalizadas o avances)");
  const idxHoy = header.indexOf("¿Qué vas a hacer hoy? (tareas o trabajo planeado)");
  if ([idxName, idxTime, idxHoy, idxAyer].some(i => i === -1)) throw new Error("Columnas requeridas no encontradas");

  const logsByUser = {};
  for (let r = 1; r < data.length; r++) {
    const row = data[r];
    const name = String(row[idxName] || "").trim();
    const raw = row[idxTime];
    const hoyTxt = String(row[idxHoy] || "").trim();
    const ayerTxt = String(row[idxAyer] || "").trim();
    if (!name || !hoyTxt) continue;
    const dObj = raw instanceof Date ? raw : parseDateStr(String(raw));
    if (!dObj) continue;

    if (!logsByUser[name]) logsByUser[name] = [];
    logsByUser[name].push({ date: dObj, hoy: hoyTxt, ayer: ayerTxt });
  }

  gamif.clearContents();
  gamif.appendRow(["Nombre", "Departamento", "TotalDailies", "Puntos", "Streak", "MaxStreak", "UltimaFecha", "Badge", "Días sin Daily", "Logros"]);

  // Usar participantes dinámicos en lugar de lista hardcodeada
  const allParticipants = getAllParticipants();

  allParticipants.forEach(name => {
    const logs = (logsByUser[name] || []).sort((a, b) => a.date - b.date);
    let total = logs.length;
    let streak = 0;
    let maxSt = 0;
    let points = 0;
    let last = null;
    let prev = null;
    let allAchievements = new Set();

    logs.forEach(l => {
      streak = prev && ((l.date - prev) / 864e5 === 1) ? streak + 1 : 1;
      maxSt = Math.max(maxSt, streak);

      // Punto base configurable
      points += gameConfig.PuntoBase || 1;

      const dateStr = Utilities.formatDate(l.date, Session.getScriptTimeZone(), "dd/MM/yyyy");

      // Bonificaciones tradicionales
      if (dateStr === luckyDateStr && gameConfig.BonusLucky) points += gameConfig.BonusLucky;
      if (wordOfDay && new RegExp(`\\b${wordOfDay}\\b`, "i").test(l.hoy + " " + l.ayer) && gameConfig.BonusWord) {
        points += gameConfig.BonusWord;
      }

      // Evaluar logros para esta entrada
      const dailyData = { timestamp: l.date, ayer: l.ayer, hoy: l.hoy, imped: "", nombre: name };
      ACHIEVEMENTS.forEach(achievement => {
        if (achievement.condition(dailyData)) {
          points += achievement.points;
          allAchievements.add(achievement.name);
        }
      });

      // Aplicar multiplicadores
      if (gameConfig.MultiplierWeekend) {
        const isWeekend = l.date.getDay() === 0 || l.date.getDay() === 6;
        if (isWeekend) {
          const basePoints = gameConfig.PuntoBase || 1;
          const extraPoints = Math.floor(basePoints * (gameConfig.MultiplierWeekend - 1));
          points += extraPoints;
        }
      }

      prev = l.date;
      last = l.date;
    });

    const daysWithout = last ? Math.floor((today - last) / 864e5) : "";
    let badge = "";
    for (let i = BADGE_MILESTONES.length - 1; i >= 0; i--) {
      if (streak >= BADGE_MILESTONES[i].days) {
        badge = BADGE_MILESTONES[i].emoji;
        break;
      }
    }

    const dept = deptMap[name]?.dept || "";
    const achievementsList = Array.from(allAchievements).join(", ");

    gamif.appendRow([
      name,
      dept,
      total,
      points,
      streak,
      maxSt,
      last ? Utilities.formatDate(last, Session.getScriptTimeZone(), "dd/MM/yyyy") : "",
      badge,
      daysWithout,
      achievementsList,
      true,  // ShieldAvailable (inicialmente disponible)
      false, // ShieldLock (inicialmente desbloqueado)
      ""     // ShieldLastUsed (vacío inicialmente)
    ]);
  });
}

/* ==========================================================
 * 4) RANKING SEMANAL Y HERRAMIENTAS ADICIONALES
 * ========================================================== */

// Genera ranking semanal y lo envía a Discord
function generateWeeklyRanking() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const resp = ss.getSheetByName("Respuestas de formulario 1");
  if (!resp) return;

  const today = new Date();
  const weekStart = new Date(today);
  weekStart.setDate(today.getDate() - today.getDay()); // Domingo de esta semana
  weekStart.setHours(0, 0, 0, 0);

  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6); // Sábado
  weekEnd.setHours(23, 59, 59, 999);

  const data = resp.getDataRange().getValues();
  const header = data[0];
  const idxName = header.indexOf(NAME_FIELD);
  const idxTime = header.indexOf("Marca temporal");
  const idxHoy = header.indexOf("¿Qué vas a hacer hoy? (tareas o trabajo planeado)");

  if ([idxName, idxTime, idxHoy].some(i => i === -1)) return;

  const weeklyStats = {};
  const gameConfig = getGameConfig();
  const { luckyDateStr, wordOfDay } = getConfigVars();

  // Procesar dailies de la semana
  for (let r = 1; r < data.length; r++) {
    const row = data[r];
    const name = String(row[idxName] || "").trim();
    const raw = row[idxTime];
    const hoyTxt = String(row[idxHoy] || "").trim();

    if (!name || !hoyTxt) continue;

    const dObj = raw instanceof Date ? raw : parseDateStr(String(raw));
    if (!dObj || dObj < weekStart || dObj > weekEnd) continue;

    if (!weeklyStats[name]) {
      weeklyStats[name] = { dailies: 0, points: 0, achievements: [] };
    }

    weeklyStats[name].dailies += 1;
    weeklyStats[name].points += gameConfig.PuntoBase || 1;

    // Calcular bonificaciones para esta entrada
    const dateStr = Utilities.formatDate(dObj, Session.getScriptTimeZone(), "dd/MM/yyyy");
    if (dateStr === luckyDateStr && gameConfig.BonusLucky) {
      weeklyStats[name].points += gameConfig.BonusLucky;
    }

    const ayerTxt = String(row[header.indexOf("¿Qué hiciste ayer? (tareas finalizadas o avances)")] || "").trim();
    if (wordOfDay && new RegExp(`\\b${wordOfDay}\\b`, "i").test(hoyTxt + " " + ayerTxt) && gameConfig.BonusWord) {
      weeklyStats[name].points += gameConfig.BonusWord;
    }

    // Evaluar logros
    const dailyData = { timestamp: dObj, ayer: ayerTxt, hoy: hoyTxt, imped: "", nombre: name };
    ACHIEVEMENTS.forEach(achievement => {
      if (achievement.condition(dailyData)) {
        weeklyStats[name].points += achievement.points;
        if (!weeklyStats[name].achievements.includes(achievement.name)) {
          weeklyStats[name].achievements.push(achievement.name);
        }
      }
    });
  }

  // Crear ranking
  const ranking = Object.entries(weeklyStats)
    .sort(([,a], [,b]) => b.points - a.points)
    .slice(0, 10); // Top 10

  if (ranking.length === 0) return;

  // Formatear mensaje para Discord
  const weekStartStr = Utilities.formatDate(weekStart, Session.getScriptTimeZone(), "dd/MM");
  const weekEndStr = Utilities.formatDate(weekEnd, Session.getScriptTimeZone(), "dd/MM");

  let msg = `🏆 **RANKING SEMANAL** (${weekStartStr} - ${weekEndStr})\n\n`;

  ranking.forEach(([name, stats], index) => {
    const medal = index === 0 ? "🥇" : index === 1 ? "🥈" : index === 2 ? "🥉" : `${index + 1}.`;
    msg += `${medal} **${name}** - ${stats.points} pts (${stats.dailies} dailies)\n`;

    if (stats.achievements.length > 0) {
      msg += `   🎯 Logros: ${stats.achievements.join(", ")}\n`;
    }
  });

  msg += `\n📊 Total participantes esta semana: ${ranking.length}`;

  UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
    method: "post",
    contentType: "application/json",
    payload: JSON.stringify({ content: msg })
  });
}

// Función para obtener estadísticas generales
function getGeneralStats() {
  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif || gamif.getLastRow() <= 1) return null;

  const data = gamif.getDataRange().getValues();
  const stats = {
    totalParticipants: data.length - 1,
    totalDailies: 0,
    totalPoints: 0,
    averageStreak: 0,
    topStreak: 0,
    activeThisWeek: 0
  };

  const today = new Date();
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

  for (let i = 1; i < data.length; i++) {
    const [, , totalDailies, points, streak, , rawLastDate] = data[i];

    stats.totalDailies += totalDailies || 0;
    stats.totalPoints += points || 0;
    stats.averageStreak += streak || 0;
    stats.topStreak = Math.max(stats.topStreak, streak || 0);

    // FIXED: Convertir timestamp a Date si es necesario
    let lastDate = rawLastDate;
    if (lastDate && typeof lastDate === 'number') {
      lastDate = new Date(lastDate);
    } else if (lastDate && typeof lastDate === 'string') {
      lastDate = parseDateStr(lastDate);
    }

    if (lastDate && lastDate >= weekAgo) {
      stats.activeThisWeek += 1;
    }
  }

  stats.averageStreak = Math.round(stats.averageStreak / stats.totalParticipants * 10) / 10;

  return stats;
}

// Función para enviar estadísticas generales a Discord
function sendGeneralStatsToDiscord() {
  const stats = getGeneralStats();
  if (!stats) return;

  let msg = `📊 **ESTADÍSTICAS GENERALES DEL SISTEMA**\n\n`;
  msg += `👥 **Participantes totales:** ${stats.totalParticipants}\n`;
  msg += `📝 **Dailies completados:** ${stats.totalDailies}\n`;
  msg += `⭐ **Puntos totales:** ${stats.totalPoints}\n`;
  msg += `🔥 **Racha promedio:** ${stats.averageStreak}\n`;
  msg += `🏆 **Racha más alta:** ${stats.topStreak}\n`;
  msg += `📈 **Activos esta semana:** ${stats.activeThisWeek}\n`;

  const gameConfig = getGameConfig();
  const { wordOfDay, luckyDateStr } = getConfigVars();

  msg += `\n⚙️ **CONFIGURACIÓN ACTUAL:**\n`;
  msg += `💡 Palabra del día: *${wordOfDay || "No definida"}*\n`;
  msg += `🍀 Lucky Day: ${luckyDateStr || "No definido"}\n`;
  msg += `🎯 Punto base: ${gameConfig.PuntoBase || 1}\n`;
  msg += `🎁 Bonus Lucky: ${gameConfig.BonusLucky || 1}\n`;
  msg += `📝 Bonus Palabra: ${gameConfig.BonusWord || 1}\n`;

  if (gameConfig.MultiplierWeekend) {
    msg += `🎉 Multiplicador fin de semana: x${gameConfig.MultiplierWeekend}\n`;
  }

  UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
    method: "post",
    contentType: "application/json",
    payload: JSON.stringify({ content: msg })
  });
}

/* ==========================================================
 * 5) FUNCIONES DE UTILIDAD Y MANTENIMIENTO
 * ========================================================== */

// Función para limpiar y reorganizar datos (mantenimiento)
function cleanupGamificationData() {
  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  if (!gamif) return;

  // Eliminar filas vacías o con nombres duplicados
  const data = gamif.getDataRange().getValues();
  const cleanData = [data[0]]; // Mantener header
  const seenNames = new Set();

  for (let i = 1; i < data.length; i++) {
    const name = String(data[i][0] || "").trim();
    if (name && !seenNames.has(name)) {
      seenNames.add(name);
      cleanData.push(data[i]);
    }
  }

  if (cleanData.length !== data.length) {
    gamif.clearContents();
    gamif.getRange(1, 1, cleanData.length, cleanData[0].length).setValues(cleanData);
  }
}

// Función para agregar participante manualmente
function addParticipant(name, department = "") {
  if (!name || typeof name !== "string") return false;

  const gamif = getOrCreateSheet(GAMIF_SHEET_NAME, [
    "Nombre", "Departamento", "TotalDailies", "Puntos", "Streak", "MaxStreak", "UltimaFecha", "Badge", "Días sin Daily", "Logros"
  ]);

  const data = gamif.getDataRange().getValues();
  const existingNames = data.slice(1).map(row => String(row[0] || "").trim().toLowerCase());

  if (existingNames.includes(name.toLowerCase())) {
    return false; // Ya existe
  }

  gamif.appendRow([name.trim(), department, 0, 0, 0, 0, "", "", "", ""]);
  return true;
}

/* ==========================================================
 * 6) FUNCIONES PARA TRIGGERS Y AUTOMATIZACIÓN
 * ========================================================== */

// Función para configurar triggers automáticos (ejecutar una vez)
function setupAutomaticTriggers() {
  // Eliminar triggers existentes
  ScriptApp.getProjectTriggers().forEach(trigger => {
    if (trigger.getHandlerFunction() === 'generateWeeklyRanking' ||
        trigger.getHandlerFunction() === 'setWordOfDay' ||
        trigger.getHandlerFunction() === 'sendProjectMotivationToDiscord' ||
        trigger.getHandlerFunction() === 'applyStreakShields') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // Trigger para ranking semanal (lunes a las 9:00 AM)
  ScriptApp.newTrigger('generateWeeklyRanking')
    .timeBased()
    .everyWeeks(1)
    .onWeekDay(ScriptApp.WeekDay.MONDAY)
    .atHour(9)
    .create();

  // Trigger para cambiar palabra del día (diario a las 8:00 AM)
  ScriptApp.newTrigger('setWordOfDay')
    .timeBased()
    .everyDays(1)
    .atHour(8)
    .create();

  // Trigger para reporte de motivación del proyecto (viernes a las 17:00)
  ScriptApp.newTrigger('sendProjectMotivationToDiscord')
    .timeBased()
    .everyWeeks(1)
    .onWeekDay(ScriptApp.WeekDay.FRIDAY)
    .atHour(17)
    .create();

  // NUEVO: Trigger para aplicar escudos automáticamente (diario a las 8:00 AM, zona Madrid)
  ScriptApp.newTrigger('applyStreakShields')
    .timeBased()
    .everyDays(1)
    .atHour(8)
    .inTimezone('Europe/Madrid')
    .create();

  console.log("Triggers automáticos configurados correctamente (incluyendo escudos de racha)");
}

// Función para crear hoja de ayuda con documentación
function createHelpSheet() {
  const helpSheet = getOrCreateSheet("📖 Ayuda", ["Función", "Descripción", "Uso"]);

  if (helpSheet.getLastRow() <= 1) {
    const helpData = [
      ["🎮 FUNCIONES PRINCIPALES", "", ""],
      ["onFormSubmit()", "Procesa automáticamente cada daily enviado", "Automático al enviar formulario"],
      ["rebuildGamificationTable()", "Recalcula toda la tabla desde cero", "Ejecutar manualmente para auditoría"],
      ["", "", ""],
      ["🏆 GAMIFICACIÓN", "", ""],
      ["generateWeeklyRanking()", "Genera ranking semanal y lo envía a Discord", "Automático los lunes o manual"],
      ["sendGeneralStatsToDiscord()", "Envía estadísticas generales a Discord", "Ejecutar manualmente"],
      ["", "", ""],
      ["🚀 MOTIVACIÓN DEL PROYECTO", "", ""],
      ["generateProjectStats()", "Genera estadísticas del trabajo del proyecto", "Para análisis interno"],
      ["sendProjectMotivationToDiscord()", "Envía reporte motivacional del proyecto", "Automático viernes o manual"],
      ["detectProjectWork(dailyData)", "Detecta si daily menciona trabajo del proyecto", "Uso interno"],
      ["calculateProjectFocus(dailyData)", "Calcula nivel de enfoque en proyecto (0-1)", "Uso interno"],
      ["", "", ""],
      ["🛡️ SISTEMA DE ESCUDO DE RACHA (MEJORADO)", "", ""],
      ["initializeDuolingoShieldSystem()", "Inicializa sistema mejorado", "Ejecutar una vez para migrar"],
      ["applyStreakShields()", "Aplica escudos automáticamente a las 8:00 AM", "Automático diario"],
      ["grantShieldOnStreak(usuario, streak)", "Otorga escudo (7 días inicial, 2 regeneración)", "Automático en dailies"],
      ["showAllShieldStatus()", "Muestra estado de escudos de todos los usuarios", "Para monitoreo"],
      ["getShieldStatus(usuario)", "Obtiene estado del escudo de un usuario", "Para debugging"],
      ["consumeShield(usuario)", "Consume escudo de un usuario manualmente", "Para testing"],
      ["testDuolingoShieldSystem()", "Prueba completa del sistema mejorado", "Ejecutar para verificar funcionamiento"],
      ["migrateDateFormatsToTimestamp()", "Convierte fechas a timestamp para precisión", "Ejecutar una vez para fix"],
      ["debugShieldDateIssue()", "Diagnóstica problemas de fechas en escudos", "Para debugging avanzado"],
      ["test24HourFix()", "Prueba el fix de las 24 horas", "Verificar lógica de tiempo"],
      ["testDailyDiscordSending()", "Prueba envío de dailies a Discord", "Para debugging de mensajes"],
      ["debugIncorrectShieldApplication()", "Verifica escudos aplicados incorrectamente", "Para debugging específico"],
      ["checkCurrentTimestamps()", "Verifica timestamps actuales en la hoja", "Para debugging de fechas"],
      ["fixInvalidDatesInSheet()", "Repara Invalid Dates recuperando de formulario", "EJECUTAR para arreglar timestamps"],
      ["restoreIncorrectlyConsumedShields()", "Restaura escudos consumidos por bug de timestamps", "EJECUTAR para reparar escudos"],
      ["manualRestoreShield(usuario, razón)", "Restaura escudo manualmente a un usuario", "manualRestoreShield('Ian', 'Error del sistema')"],
      ["bulkRestoreShields([usuarios], razón)", "Restaura escudos a múltiples usuarios", "bulkRestoreShields(['Ian','Laura'], 'Bug fix')"],
      ["fixBugVictimsShields()", "Restaura escudos de Ian, Laura y Paola (víctimas del bug)", "EJECUTAR para fix directo"],
      ["", "", ""],
      ["🎖️ SISTEMA DE LOGROS ÚNICOS", "", ""],
      ["showAllAchievements()", "Muestra todos los logros disponibles", "Para ver qué logros existen"],
      ["showUserAchievements(usuario)", "Muestra logros de un usuario específico", "showUserAchievements('Juan')"],
      ["getAchievementRanking()", "Ranking de usuarios por logros obtenidos", "Ver quién tiene más logros"],
      ["manualGrantAchievement(usuario, logro)", "Otorgar logro manualmente", "manualGrantAchievement('Juan', 'early_bird')"],
      ["getUserAchievementEmojis(usuario)", "Obtiene emojis de logros del usuario", "Para mostrar junto al nombre"],
      ["recalculateExistingAchievements()", "Encuentra logros ya obtenidos pero no notificados", "EJECUTAR UNA VEZ tras implementar"],
      ["recalculateUserAchievements(usuario)", "Recalcula logros de un usuario específico", "Más seguro para testing"],
      ["forceNotifyPendingAchievements(usuario)", "Fuerza notificación de logros pendientes", "forceNotifyPendingAchievements('Juan')"],
      ["getPendingNotifications(usuario)", "Ve logros pendientes de notificar", "Para debugging"],
      ["clearAchievementCache()", "Limpia cache de logros", "Usar si hay errores de acceso"],
      ["updateUserTotalPoints(usuario)", "Recalcula puntos totales de un usuario", "Incluye puntos de logros"],
      ["updateAllUserPoints()", "Recalcula puntos de todos los usuarios", "EJECUTAR tras otorgar logros atrasados"],
      ["testCollaborativeDetection(usuario, ayer, hoy, imped)", "Prueba detección de logro colaborativo", "Para debugging"],
      ["testEmojiCleaning()", "Prueba limpieza de emojis duplicados", "Para verificar nombres display"],
      ["checkDuplicateAchievements(usuario)", "Verifica logros duplicados en BD", "Para debugging"],
      ["createDisplayName(usuario, badge, extra)", "Crea nombre sin emojis duplicados", "Uso interno optimizado"],
      ["", "", ""],
      ["⚙️ CONFIGURACIÓN", "", ""],
      ["setWordOfDay()", "Cambia la palabra del día aleatoriamente", "Automático diario o manual"],
      ["setWeeklyLuckyDay()", "Establece nuevo día de suerte semanal", "Ejecutar manualmente"],
      ["setupAutomaticTriggers()", "Configura triggers automáticos", "Ejecutar una vez para automatizar"],
      ["", "", ""],
      ["🛠️ MANTENIMIENTO", "", ""],
      ["updateStreaksPreservingData()", "Actualiza streaks sin perder datos existentes", "RECOMENDADO para uso regular"],
      ["verifyAllStreaks()", "Muestra estado actual de todos los streaks", "Para verificar cálculos"],
      ["cleanupGamificationData()", "Limpia datos duplicados o vacíos", "Ejecutar ocasionalmente"],
      ["addParticipant(nombre, depto)", "Agrega participante manualmente", "addParticipant('Juan', 'IT')"],
      ["getAllParticipants()", "Obtiene lista de todos los participantes", "Para verificar participantes activos"],
      ["", "", ""],
      ["📊 CATEGORÍAS DE LOGROS ÚNICOS", "", ""],
      ["� Inicio", "Primer Paso - Tu primera daily", "+5 puntos"],
      ["📈 Volumen", "Francotirador (10), Arquero (25), Veterano (50), Leyenda (100)", "10-50 puntos"],
      ["🔥 Racha", "En Llamas (5), Supernova (10), Imparable (15), Rey (30)", "15-75 puntos"],
      ["⏰ Horario", "Madrugador (<9am), Búho Nocturno (>22pm), Guerrero Fin de Semana", "10-15 puntos"],
      ["📝 Contenido", "Escritor (300+ chars), Creativo (emojis), Colaborativo, Innovador", "5-10 puntos"],
      ["🚀 Proyecto", "10 logros específicos del proyecto (Guerrero, Solucionador, etc.)", "11-25 puntos"],
      ["🎪 Especial", "Amuleto Suerte, Camaleón, Velocista, Showman, Maestro Palabras", "15-30 puntos"],
      ["🏢 Departamento", "Embajador, Líder de Equipo", "20-25 puntos"],
      ["🎊 Eventos", "Año Nuevo, Navidad, Viernes Guerrero", "8-10 puntos"],
      ["🌟 Legendarios", "Logros súper raros y de pura suerte", "29-300 puntos"],
      ["", "", ""],
      ["🌟 LOGROS LEGENDARIOS (SÚPER RAROS)", "", ""],
      ["⏰ Tiempo Exacto", "Guerrero Medianoche (00:00:00), Viajero Tiempo (12:34:56)", "88-100 puntos"],
      ["🌌 Fechas Especiales", "Maestro Eclipse, Profeta Palíndromo, Leyenda Bisiesto", "29-150 puntos"],
      ["🔢 Matemáticos", "Fibonacci ≥21, Primo ≥67, Binario ≥64, Ratio Dorado", "64-161 puntos"],
      ["🎯 Específicos", "Marcador Siglo (100 dailies), Tormenta Perfecta", "200-300 puntos"],
      ["🍀 Pura Suerte", "Combinaciones de palabras específicas", "42-137 puntos"],
      ["🎪 Coincidencias", "Conteos exactos, categorías temáticas", "50-123 puntos"],
      ["🤯 Meta/Recursivos", "Mencionar 'daily', 'logro', paradojas temporales", "101-121 puntos"],
      ["", "", ""],
      ["🛡️ SISTEMA DE ESCUDO DE RACHA (MEJORADO)", "", ""],
      ["Primer escudo", "Automático al alcanzar 7 días consecutivos", "Solo 1 escudo a la vez"],
      ["Uso automático", "Se aplica a las 8:00 AM si no hay daily en 24h", "Mantiene racha viva"],
      ["Regeneración rápida", "Solo 2 días consecutivos después de usar uno", "Más accesible que Duolingo"],
      ["Simplicidad", "7 días → escudo → 2 días → nuevo escudo", "Sistema motivador"],
      ["", "", ""],
      ["🌟 EJEMPLOS DE LOGROS LEGENDARIOS", "", ""],
      ["🍀 Maestro Serendipia", "Usar 'suerte', 'casualidad' y 'destino' en misma daily", "77 puntos"],
      ["🔴 Glitch Matrix", "Mencionar 'bug', 'error' y 'matrix' juntos", "99 puntos"],
      ["⏳ Paradoja Temporal", "'ayer' en sección ayer, 'hoy' y 'mañana' en hoy", "121 puntos"],
      ["🌙 Guerrero Medianoche", "Daily exactamente a las 00:00:00", "100 puntos"],
      ["🌘 Maestro Eclipse", "Daily durante eclipse solar real", "150 puntos"],
      ["🔮 Profeta Palíndromo", "Daily en fecha palíndromo (12/02/2021)", "75 puntos"],
      ["🌀 Maestro Fibonacci", "Total dailies = Fibonacci ≥21 (21,34,55,89...)", "89 puntos"],
      ["🔢 Perfeccionista Primo", "Total dailies = primo ≥67 (67,71,73,79...)", "67 puntos"],
      ["💾 Profeta Binario", "Total dailies = potencia 2 ≥64 (64,128,256...)", "64 puntos"],
      ["💯 Marcador Siglo", "Exactamente 100 dailies completadas", "200 puntos"],
      ["⛈️ Tormenta Perfecta", "Lucky Day + palabra día + 7+ logros mismo día", "300 puntos"],
      ["😎 Profeta Emojis", "Exactamente 5 emojis diferentes en daily", "55 puntos"],
      ["📏 Perfeccionista Longitud", "Exactamente 123 caracteres total", "123 puntos"],
      ["🌈 Espectro Colores", "Mencionar 4 colores diferentes", "70 puntos"],
      ["🦁 Susurrador Animales", "Mencionar 5 animales diferentes", "65 puntos"],
      ["💻 Maestro Tech Stack", "Mencionar 4 tecnologías programación", "80 puntos"],
      ["🌌 Filósofo Universo", "'existencia'+'consciencia'+'realidad'+'infinito'", "137 puntos"],
      ["", "", ""],
      ["⚙️ CONFIGURACIÓN FLEXIBLE", "", ""],
      ["Hoja 'GameConfig'", "Configura puntos y bonificaciones", "Editar valores directamente"],
      ["PuntoBase", "Puntos por completar daily", "Por defecto: 1"],
      ["BonusLucky", "Bonus en Lucky Day", "Por defecto: 1"],
      ["BonusWord", "Bonus por usar palabra del día", "Por defecto: 1"],
      ["MultiplierWeekend", "Multiplicador fin de semana", "Por defecto: desactivado"]
    ];

    helpData.forEach(row => helpSheet.appendRow(row));

    // Formatear la hoja
    helpSheet.getRange(1, 1, helpSheet.getLastRow(), 3).setWrap(true);
    helpSheet.setColumnWidth(1, 250);
    helpSheet.setColumnWidth(2, 350);
    helpSheet.setColumnWidth(3, 200);
  }
}

/* ==========================================================
 * 7) FUNCIONES DE TESTING Y DEBUGGING
 * ========================================================== */

// Función para probar el sistema con datos de ejemplo
function testGamificationSystem() {
  console.log("🧪 Iniciando pruebas del sistema...");

  // Probar configuración
  const config = getGameConfig();
  console.log("✅ Configuración cargada:", config);

  // Probar participantes dinámicos
  const participants = getAllParticipants();
  console.log("✅ Participantes encontrados:", participants.length);

  // Probar logros
  const testData = {
    timestamp: new Date("2024-01-15T08:30:00"), // Lunes 8:30 AM
    ayer: "Completé las tareas de desarrollo y revisé el código con el equipo",
    hoy: "Voy a implementar las nuevas funcionalidades y hacer testing",
    imped: "",
    nombre: "TestUser"
  };

  let achievementsEarned = [];
  ACHIEVEMENTS.forEach(achievement => {
    if (achievement.condition(testData)) {
      achievementsEarned.push(achievement.name);
    }
  });

  console.log("✅ Logros que se activarían:", achievementsEarned);
  console.log("🎉 Sistema funcionando correctamente!");

  return {
    config,
    participantsCount: participants.length,
    achievementsEarned
  };
}

// Función específica para probar el sistema de streaks
function testStreakSystem() {
  console.log("🧪 PROBANDO SISTEMA DE STREAKS...");

  // 1. Verificar estado actual
  console.log("\n1️⃣ Estado actual de streaks:");
  const currentState = verifyAllStreaks();

  // 2. Actualizar streaks preservando datos
  console.log("\n2️⃣ Actualizando streaks...");
  updateStreaksPreservingData();

  // 3. Verificar después de la actualización
  console.log("\n3️⃣ Estado después de actualización:");
  const newState = verifyAllStreaks();

  // 4. Mostrar cambios
  console.log("\n4️⃣ RESUMEN DE CAMBIOS:");
  currentState.forEach((current, index) => {
    const updated = newState[index];
    if (current && updated && (current.streak !== updated.streak || current.maxStreak !== updated.maxStreak)) {
      console.log(`📊 ${current.name}:`);
      console.log(`   Streak: ${current.streak} → ${updated.streak}`);
      console.log(`   Max: ${current.maxStreak} → ${updated.maxStreak}`);
    }
  });

  console.log("\n✅ Prueba de sistema de streaks completada");

  return {
    participantsChecked: currentState.length,
    changesDetected: currentState.filter((current, index) => {
      const updated = newState[index];
      return current && updated && (current.streak !== updated.streak || current.maxStreak !== updated.maxStreak);
    }).length
  };
}

/* ==========================================================
 * 8) FUNCIONES DE GESTIÓN DE LOGROS ÚNICOS
 * ========================================================== */

// Mostrar todos los logros disponibles
function showAllAchievements() {
  console.log("🏆 TODOS LOS LOGROS DISPONIBLES:");
  console.log("=" .repeat(60));

  const categories = {};
  UNIQUE_ACHIEVEMENTS.forEach(ach => {
    if (!categories[ach.category]) {
      categories[ach.category] = [];
    }
    categories[ach.category].push(ach);
  });

  Object.keys(categories).forEach(category => {
    console.log(`\n📂 ${category.toUpperCase()}:`);
    categories[category].forEach(ach => {
      console.log(`   ${ach.emoji} ${ach.name} (${ach.points} pts) - ${ach.description}`);
    });
  });

  console.log(`\n📊 Total de logros: ${UNIQUE_ACHIEVEMENTS.length}`);
}

// Mostrar logros de un usuario específico
function showUserAchievements(userName) {
  const userAchievements = getUserAchievements(userName);

  console.log(`🏆 LOGROS DE ${userName.toUpperCase()}:`);
  console.log("=" .repeat(50));

  if (userAchievements.length === 0) {
    console.log("❌ Este usuario no tiene logros aún.");
    return;
  }

  const categories = {};
  userAchievements.forEach(userAch => {
    const achievement = UNIQUE_ACHIEVEMENTS.find(ach => ach.id === userAch.id);
    if (achievement) {
      if (!categories[achievement.category]) {
        categories[achievement.category] = [];
      }
      categories[achievement.category].push({
        ...achievement,
        dateEarned: userAch.date
      });
    }
  });

  let totalPoints = 0;
  Object.keys(categories).forEach(category => {
    console.log(`\n📂 ${category}:`);
    categories[category].forEach(ach => {
      console.log(`   ${ach.emoji} ${ach.name} (${ach.points} pts) - ${Utilities.formatDate(ach.dateEarned, Session.getScriptTimeZone(), "dd/MM/yyyy")}`);
      totalPoints += ach.points;
    });
  });

  console.log(`\n📊 Total logros: ${userAchievements.length}/${UNIQUE_ACHIEVEMENTS.length}`);
  console.log(`⭐ Puntos por logros: ${totalPoints}`);

  return {
    totalAchievements: userAchievements.length,
    totalPoints: totalPoints,
    categories: Object.keys(categories)
  };
}

// Ranking de usuarios por logros obtenidos
function getAchievementRanking() {
  const achievementSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName("UserAchievements");
  if (!achievementSheet) {
    console.log("❌ No hay datos de logros aún.");
    return [];
  }

  const data = achievementSheet.getDataRange().getValues();
  const userStats = {};

  for (let i = 1; i < data.length; i++) {
    const [name, , date, points] = data[i];
    if (!userStats[name]) {
      userStats[name] = {
        achievements: 0,
        points: 0,
        lastAchievement: date
      };
    }
    userStats[name].achievements += 1;
    userStats[name].points += points || 0;
    if (date > userStats[name].lastAchievement) {
      userStats[name].lastAchievement = date;
    }
  }

  const ranking = Object.entries(userStats)
    .sort(([,a], [,b]) => b.achievements - a.achievements || b.points - a.points)
    .map(([name, stats], index) => ({
      rank: index + 1,
      name,
      achievements: stats.achievements,
      points: stats.points,
      lastAchievement: stats.lastAchievement
    }));

  console.log("🏆 RANKING POR LOGROS:");
  console.log("=" .repeat(50));

  ranking.slice(0, 10).forEach(user => {
    const medal = user.rank === 1 ? "🥇" : user.rank === 2 ? "🥈" : user.rank === 3 ? "🥉" : `${user.rank}.`;
    console.log(`${medal} ${user.name} - ${user.achievements} logros (${user.points} pts)`);
  });

  return ranking;
}

// Función para otorgar logro manualmente (para testing o casos especiales)
function manualGrantAchievement(userName, achievementId) {
  const achievement = UNIQUE_ACHIEVEMENTS.find(ach => ach.id === achievementId);
  if (!achievement) {
    console.log(`❌ Logro '${achievementId}' no encontrado.`);
    return false;
  }

  if (hasUserAchievement(userName, achievementId)) {
    console.log(`❌ ${userName} ya tiene el logro '${achievement.name}'.`);
    return false;
  }

  // Obtener puntos antes del otorgamiento
  const gamif = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(GAMIF_SHEET_NAME);
  let oldPoints = 0;
  if (gamif) {
    const data = gamif.getDataRange().getValues();
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === userName) {
        oldPoints = data[i][3] || 0; // Columna "Puntos"
        break;
      }
    }
  }

  if (grantAchievement(userName, achievementId)) {
    // Obtener puntos después del otorgamiento
    let newPoints = oldPoints;
    if (gamif) {
      const data = gamif.getDataRange().getValues();
      for (let i = 1; i < data.length; i++) {
        if (data[i][0] === userName) {
          newPoints = data[i][3] || 0;
          break;
        }
      }
    }

    const pointsAdded = newPoints - oldPoints;

    console.log(`✅ Logro '${achievement.name}' otorgado a ${userName}!`);
    console.log(`💰 Puntos actualizados: ${oldPoints} → ${newPoints} (+${pointsAdded})`);

    // Enviar notificación a Discord
    const displayName = createDisplayName(userName);
    const msg = `🎉 **¡LOGRO ESPECIAL OTORGADO!**\n${achievement.emoji} **${displayName}** ha desbloqueado **${achievement.name}**!\n📝 ${achievement.description}\n⭐ +${achievement.points} puntos\n💰 **Puntos totales:** ${newPoints}`;

    UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
      method: "post",
      contentType: "application/json",
      payload: JSON.stringify({ content: msg })
    });

    return true;
  }

  return false;
}

// Función para probar la limpieza de emojis duplicados
function testEmojiCleaning() {
  console.log("🧪 PROBANDO LIMPIEZA DE EMOJIS DUPLICADOS:");

  const testCases = [
    "Rubén 🤝🔗 🔥 🔗",
    "Juan 🎯🏹🌅🔥🎯",
    "María 🌟🚀🌟🏆",
    "Pedro 🔥🔥🔥",
    "Ana"
  ];

  testCases.forEach(testCase => {
    const cleaned = removeDuplicateEmojis(testCase);
    console.log(`   "${testCase}" → "${cleaned}"`);
  });

  // Probar función createDisplayName
  console.log("\n🧪 PROBANDO createDisplayName:");
  const testName = "Rubén";
  const testBadge = "🔥";
  const testExtra = " 🔗";

  // Simular que el usuario tiene logros con emojis 🤝🔗
  const result = createDisplayName(testName, testBadge, testExtra);
  console.log(`   createDisplayName("${testName}", "${testBadge}", "${testExtra}") → "${result}"`);

  return true;
}

// Función para verificar logros duplicados en la base de datos
function checkDuplicateAchievements(userName) {
  console.log(`🔍 VERIFICANDO LOGROS DUPLICADOS PARA ${userName}:`);

  const data = getAchievementData();
  const userLogros = {};

  for (let i = 1; i < data.length; i++) {
    const [name, achievementId, date] = data[i];
    if (name === userName) {
      if (!userLogros[achievementId]) {
        userLogros[achievementId] = [];
      }
      userLogros[achievementId].push(date);
    }
  }

  let duplicatesFound = false;
  Object.keys(userLogros).forEach(achievementId => {
    if (userLogros[achievementId].length > 1) {
      console.log(`   ❌ DUPLICADO: ${achievementId} (${userLogros[achievementId].length} veces)`);
      userLogros[achievementId].forEach((date, index) => {
        console.log(`      ${index + 1}. ${Utilities.formatDate(date, Session.getScriptTimeZone(), "dd/MM/yyyy HH:mm")}`);
      });
      duplicatesFound = true;
    } else {
      const achievement = UNIQUE_ACHIEVEMENTS.find(ach => ach.id === achievementId);
      const achievementName = achievement ? achievement.name : achievementId;
      console.log(`   ✅ ${achievementName}: 1 vez`);
    }
  });

  if (!duplicatesFound) {
    console.log("   ✅ No se encontraron duplicados");
  }

  return userLogros;
}

// Función para probar la detección de logros con un daily específico
function testCollaborativeDetection(userName, ayer, hoy, imped) {
  console.log(`🧪 PROBANDO DETECCIÓN DE LOGRO COLABORATIVO PARA ${userName}:`);
  console.log(`📝 Ayer: ${ayer}`);
  console.log(`📝 Hoy: ${hoy}`);
  console.log(`📝 Impedimentos: ${imped}`);

  const testData = {
    timestamp: new Date(),
    ayer: ayer,
    hoy: hoy,
    imped: imped,
    nombre: userName
  };

  const collaborativeAchievement = UNIQUE_ACHIEVEMENTS.find(ach => ach.id === "collaborative");

  if (collaborativeAchievement) {
    const shouldGetAchievement = collaborativeAchievement.condition(testData);
    console.log(`🔍 ¿Debería obtener logro Colaborativo? ${shouldGetAchievement ? "✅ SÍ" : "❌ NO"}`);

    if (shouldGetAchievement) {
      console.log(`🎉 ${userName} debería obtener el logro "Colaborativo" 🔗`);

      // Verificar si ya lo tiene
      const alreadyHas = hasUserAchievement(userName, "collaborative");
      console.log(`🏆 ¿Ya tiene el logro? ${alreadyHas ? "✅ SÍ" : "❌ NO"}`);

      if (!alreadyHas) {
        console.log(`💡 Puedes otorgárselo manualmente con:`);
        console.log(`   manualGrantAchievement("${userName}", "collaborative")`);
      }
    }

    return shouldGetAchievement;
  }

  return false;
}

// Función para recalcular logros existentes y marcarlos como pendientes de notificar (versión robusta)
function recalculateExistingAchievements() {
  console.log("🔄 RECALCULANDO LOGROS EXISTENTES PARA NOTIFICACIÓN...");

  try {
    const participants = getAllParticipants();
    let logrosEncontrados = 0;
    let errores = 0;

    console.log(`👥 Procesando ${participants.length} participantes...`);

    for (let i = 0; i < participants.length; i++) {
      const userName = participants[i];
      console.log(`📊 Evaluando ${userName} (${i + 1}/${participants.length})...`);

      try {
        const userStats = calculateUserStats(userName);

        if (userStats) {
          for (const achievement of UNIQUE_ACHIEVEMENTS) {
            try {
              // Solo evaluar si el usuario NO tiene ya este logro
              if (!hasUserAchievement(userName, achievement.id)) {
                // Para logros que no dependen de daily específico, usar datos dummy
                const dummyDailyData = {
                  timestamp: new Date(),
                  ayer: "",
                  hoy: "",
                  imped: "",
                  nombre: userName
                };

                if (achievement.condition(dummyDailyData, userStats)) {
                  if (grantAchievement(userName, achievement.id)) {
                    console.log(`   ✅ ${achievement.name} - PENDIENTE DE NOTIFICAR`);
                    logrosEncontrados++;
                  }
                }
              }
            } catch (achievementError) {
              console.log(`   ❌ Error evaluando logro ${achievement.name}: ${achievementError.message}`);
              errores++;
            }
          }
        }

        // Pausa pequeña cada 3 usuarios para evitar límites de API
        if ((i + 1) % 3 === 0) {
          console.log("⏸️ Pausa breve para evitar límites de API...");
          Utilities.sleep(1000); // 1 segundo
        }

      } catch (userError) {
        console.log(`❌ Error procesando ${userName}: ${userError.message}`);
        errores++;
      }
    }

    console.log(`🎉 Recálculo completado!`);
    console.log(`✅ ${logrosEncontrados} logros encontrados y marcados como pendientes de notificar.`);
    if (errores > 0) {
      console.log(`⚠️ ${errores} errores encontrados durante el proceso.`);
    }
    console.log("💡 Estos logros se notificarán en el próximo daily de cada usuario.");

    return {
      logrosEncontrados,
      errores,
      participantesProcesados: participants.length
    };

  } catch (error) {
    console.log(`❌ Error crítico en recalculateExistingAchievements: ${error.message}`);
    console.log("🔧 Intenta ejecutar clearAchievementCache() y vuelve a intentar.");
    throw error;
  }
}

// Función para recalcular logros de un solo usuario (más segura para testing)
function recalculateUserAchievements(userName) {
  console.log(`🔄 RECALCULANDO LOGROS PARA ${userName}...`);

  try {
    const userStats = calculateUserStats(userName);
    let logrosEncontrados = 0;

    if (!userStats) {
      console.log(`❌ No se pudieron obtener estadísticas para ${userName}`);
      return 0;
    }

    UNIQUE_ACHIEVEMENTS.forEach(achievement => {
      try {
        if (!hasUserAchievement(userName, achievement.id)) {
          const dummyDailyData = {
            timestamp: new Date(),
            ayer: "",
            hoy: "",
            imped: "",
            nombre: userName
          };

          if (achievement.condition(dummyDailyData, userStats)) {
            if (grantAchievement(userName, achievement.id)) {
              console.log(`   ✅ ${achievement.name} - PENDIENTE DE NOTIFICAR`);
              logrosEncontrados++;
            }
          }
        }
      } catch (error) {
        console.log(`   ❌ Error evaluando ${achievement.name}: ${error.message}`);
      }
    });

    console.log(`✅ ${logrosEncontrados} logros encontrados para ${userName}`);
    return logrosEncontrados;

  } catch (error) {
    console.log(`❌ Error procesando ${userName}: ${error.message}`);
    return 0;
  }
}

// Función para forzar notificación de logros pendientes de un usuario específico
function forceNotifyPendingAchievements(userName) {
  const pendingNotifications = getPendingNotifications(userName);

  if (pendingNotifications.length === 0) {
    console.log(`❌ ${userName} no tiene logros pendientes de notificar.`);
    return false;
  }

  console.log(`📢 Notificando ${pendingNotifications.length} logros pendientes de ${userName}...`);

  const displayName = createDisplayName(userName);

  let msg = `🎉 **¡LOGROS ÚNICOS DE ${displayName.toUpperCase()}!**\n`;
  msg += `📢 **Notificación especial de logros obtenidos:**\n\n`;

  let totalPoints = 0;
  pendingNotifications.forEach(ach => {
    const dateStr = Utilities.formatDate(ach.dateEarned, Session.getScriptTimeZone(), "dd/MM/yyyy");
    msg += `${ach.emoji} **${ach.name}** - ${ach.description} (${dateStr}) (+${ach.points} pts)\n`;
    totalPoints += ach.points;
  });

  msg += `\n⭐ **Total puntos por logros:** ${totalPoints}`;
  msg += `\n🏆 **Total logros obtenidos:** ${pendingNotifications.length}`;

  // Marcar como notificados
  const achievementIds = pendingNotifications.map(ach => ach.id);
  markAchievementsAsNotified(userName, achievementIds);

  // Enviar a Discord
  UrlFetchApp.fetch(DISCORD_WEBHOOK_URL, {
    method: "post",
    contentType: "application/json",
    payload: JSON.stringify({ content: msg })
  });

  console.log(`✅ Notificación enviada para ${userName}`);
  return true;
}

// Función para probar el sistema completo de logros únicos
function testAchievementSystem() {
  console.log("🧪 PROBANDO SISTEMA DE LOGROS ÚNICOS...");

  // 1. Mostrar todos los logros disponibles
  console.log("\n1️⃣ Logros disponibles:");
  showAllAchievements();

  // 2. Probar con un usuario de ejemplo
  const participants = getAllParticipants();
  if (participants.length > 0) {
    const testUser = participants[0];
    console.log(`\n2️⃣ Probando con usuario: ${testUser}`);

    // Mostrar logros actuales del usuario
    showUserAchievements(testUser);

    // Mostrar emojis del usuario
    const emojis = getUserAchievementEmojis(testUser);
    console.log(`\n👤 Emojis de ${testUser}: ${emojis || "Ninguno"}`);

    // Calcular estadísticas del usuario
    const stats = calculateUserStats(testUser);
    if (stats) {
      console.log(`\n📊 Estadísticas de ${testUser}:`);
      console.log(`   Total dailies: ${stats.totalDailies}`);
      console.log(`   Streak actual: ${stats.currentStreak}`);
      console.log(`   Días de semana completados: ${stats.daysOfWeekCompleted}/7`);
      console.log(`   Lucky days: ${stats.luckyDaysHit}`);
      console.log(`   Palabras del día usadas: ${stats.wordOfDayUsed}`);
    }
  }

  // 3. Mostrar ranking de logros
  console.log("\n3️⃣ Ranking por logros:");
  getAchievementRanking();

  // 4. Probar notificaciones pendientes
  if (participants.length > 0) {
    const testUser = participants[0];
    console.log(`\n4️⃣ Logros pendientes de notificar para ${testUser}:`);
    const pending = getPendingNotifications(testUser);
    if (pending.length > 0) {
      pending.forEach(ach => {
        console.log(`   🔔 ${ach.emoji} ${ach.name} - ${ach.description}`);
      });
    } else {
      console.log("   ✅ No hay logros pendientes de notificar");
    }
  }

  // 5. Funciones útiles
  console.log("\n5️⃣ Funciones útiles:");
  console.log("   📊 recalculateExistingAchievements() - Encuentra logros ya obtenidos");
  console.log("   📢 forceNotifyPendingAchievements('usuario') - Fuerza notificación");
  console.log("   🎯 manualGrantAchievement('usuario', 'logro_id') - Otorga manualmente");

  console.log("\n✅ Prueba del sistema de logros completada!");

  return {
    totalAchievements: UNIQUE_ACHIEVEMENTS.length,
    participantsTested: participants.length,
    categoriesAvailable: [...new Set(UNIQUE_ACHIEVEMENTS.map(ach => ach.category))].length,
    pendingNotifications: participants.length > 0 ? getPendingNotifications(participants[0]).length : 0
  };
}
