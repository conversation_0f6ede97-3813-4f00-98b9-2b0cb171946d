/**
 * Cache Utilities
 * Following Single Responsibility Principle - only handles caching operations
 */

class CacheUtils {
  /**
   * Get cache service instance
   * @returns {GoogleAppsScript.Cache.Cache}
   */
  static getCache() {
    return CacheService.getScriptCache();
  }

  /**
   * Set cache value
   * @param {string} key - Cache key
   * @param {*} value - Value to cache
   * @param {number} expirationInSeconds - Expiration time in seconds
   * @returns {boolean}
   */
  static set(key, value, expirationInSeconds = 3600) {
    try {
      const cache = this.getCache();
      const serializedValue = JSON.stringify(value);
      cache.put(key, serializedValue, expirationInSeconds);
      return true;
    } catch (error) {
      console.log(`❌ Error setting cache for key ${key}: ${error.message}`);
      return false;
    }
  }

  /**
   * Get cache value
   * @param {string} key - Cache key
   * @returns {*} Cached value or null if not found
   */
  static get(key) {
    try {
      const cache = this.getCache();
      const serializedValue = cache.get(key);
      
      if (serializedValue === null) {
        return null;
      }
      
      return JSON.parse(serializedValue);
    } catch (error) {
      console.log(`❌ Error getting cache for key ${key}: ${error.message}`);
      return null;
    }
  }

  /**
   * Remove cache value
   * @param {string} key - Cache key
   * @returns {boolean}
   */
  static remove(key) {
    try {
      const cache = this.getCache();
      cache.remove(key);
      return true;
    } catch (error) {
      console.log(`❌ Error removing cache for key ${key}: ${error.message}`);
      return false;
    }
  }

  /**
   * Clear all cache
   * @returns {boolean}
   */
  static clear() {
    try {
      const cache = this.getCache();
      cache.removeAll();
      return true;
    } catch (error) {
      console.log(`❌ Error clearing cache: ${error.message}`);
      return false;
    }
  }

  /**
   * Get or set cache value with function
   * @param {string} key - Cache key
   * @param {Function} valueFunction - Function to generate value if not cached
   * @param {number} expirationInSeconds - Expiration time in seconds
   * @returns {*} Cached or generated value
   */
  static getOrSet(key, valueFunction, expirationInSeconds = 3600) {
    let value = this.get(key);
    
    if (value === null) {
      try {
        value = valueFunction();
        this.set(key, value, expirationInSeconds);
      } catch (error) {
        console.log(`❌ Error generating cache value for key ${key}: ${error.message}`);
        return null;
      }
    }
    
    return value;
  }

  /**
   * Set multiple cache values
   * @param {Object} keyValuePairs - Object with key-value pairs
   * @param {number} expirationInSeconds - Expiration time in seconds
   * @returns {boolean}
   */
  static setMultiple(keyValuePairs, expirationInSeconds = 3600) {
    try {
      const cache = this.getCache();
      const serializedPairs = {};
      
      for (const [key, value] of Object.entries(keyValuePairs)) {
        serializedPairs[key] = JSON.stringify(value);
      }
      
      cache.putAll(serializedPairs, expirationInSeconds);
      return true;
    } catch (error) {
      console.log(`❌ Error setting multiple cache values: ${error.message}`);
      return false;
    }
  }

  /**
   * Get multiple cache values
   * @param {Array} keys - Array of cache keys
   * @returns {Object}
   */
  static getMultiple(keys) {
    try {
      const cache = this.getCache();
      const serializedValues = cache.getAll(keys);
      const values = {};
      
      for (const [key, serializedValue] of Object.entries(serializedValues)) {
        if (serializedValue !== null) {
          try {
            values[key] = JSON.parse(serializedValue);
          } catch (parseError) {
            console.log(`❌ Error parsing cached value for key ${key}: ${parseError.message}`);
            values[key] = null;
          }
        } else {
          values[key] = null;
        }
      }
      
      return values;
    } catch (error) {
      console.log(`❌ Error getting multiple cache values: ${error.message}`);
      return {};
    }
  }

  /**
   * Check if cache key exists
   * @param {string} key - Cache key
   * @returns {boolean}
   */
  static exists(key) {
    return this.get(key) !== null;
  }

  /**
   * Generate cache key for user data
   * @param {string} userName - User name
   * @param {string} dataType - Type of data
   * @returns {string}
   */
  static generateUserCacheKey(userName, dataType) {
    return `user_${userName}_${dataType}`;
  }

  /**
   * Generate cache key for achievement data
   * @param {string} userName - User name
   * @returns {string}
   */
  static generateAchievementCacheKey(userName) {
    return `achievements_${userName}`;
  }

  /**
   * Generate cache key for leaderboard
   * @param {string} sortBy - Sort criteria
   * @param {string} department - Department (optional)
   * @returns {string}
   */
  static generateLeaderboardCacheKey(sortBy, department = null) {
    const deptSuffix = department ? `_${department}` : '';
    return `leaderboard_${sortBy}${deptSuffix}`;
  }

  /**
   * Cache user statistics
   * @param {string} userName - User name
   * @param {Object} stats - User statistics
   * @param {number} expirationInSeconds - Expiration time
   * @returns {boolean}
   */
  static cacheUserStats(userName, stats, expirationInSeconds = 1800) {
    const key = this.generateUserCacheKey(userName, 'stats');
    return this.set(key, stats, expirationInSeconds);
  }

  /**
   * Get cached user statistics
   * @param {string} userName - User name
   * @returns {Object|null}
   */
  static getCachedUserStats(userName) {
    const key = this.generateUserCacheKey(userName, 'stats');
    return this.get(key);
  }

  /**
   * Cache leaderboard data
   * @param {string} sortBy - Sort criteria
   * @param {Array} leaderboard - Leaderboard data
   * @param {string} department - Department (optional)
   * @param {number} expirationInSeconds - Expiration time
   * @returns {boolean}
   */
  static cacheLeaderboard(sortBy, leaderboard, department = null, expirationInSeconds = 900) {
    const key = this.generateLeaderboardCacheKey(sortBy, department);
    return this.set(key, leaderboard, expirationInSeconds);
  }

  /**
   * Get cached leaderboard data
   * @param {string} sortBy - Sort criteria
   * @param {string} department - Department (optional)
   * @returns {Array|null}
   */
  static getCachedLeaderboard(sortBy, department = null) {
    const key = this.generateLeaderboardCacheKey(sortBy, department);
    return this.get(key);
  }

  /**
   * Invalidate user-related cache
   * @param {string} userName - User name
   * @returns {boolean}
   */
  static invalidateUserCache(userName) {
    const keys = [
      this.generateUserCacheKey(userName, 'stats'),
      this.generateAchievementCacheKey(userName)
    ];
    
    let success = true;
    keys.forEach(key => {
      if (!this.remove(key)) {
        success = false;
      }
    });
    
    return success;
  }

  /**
   * Invalidate leaderboard cache
   * @returns {boolean}
   */
  static invalidateLeaderboardCache() {
    // Since we can't enumerate cache keys, we'll use a timestamp-based approach
    // This is a limitation of Google Apps Script cache service
    const timestamp = Date.now();
    return this.set('leaderboard_invalidation', timestamp, 86400); // 24 hours
  }

  /**
   * Check if leaderboard cache is valid
   * @returns {boolean}
   */
  static isLeaderboardCacheValid() {
    const lastInvalidation = this.get('leaderboard_invalidation');
    if (!lastInvalidation) return true;
    
    const cacheAge = Date.now() - lastInvalidation;
    return cacheAge < AppConfig.CACHE_CONFIG.ACHIEVEMENT_CACHE_DURATION;
  }
}
