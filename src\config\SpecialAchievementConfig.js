/**
 * Special Achievement Configuration
 * Following Single Responsibility Principle - only manages special and event-based achievements
 */

class SpecialAchievementConfig {
  static get SPECIAL_ACHIEVEMENTS() {
    return [
      // === SPECIAL/RARE ACHIEVEMENTS ===
      {
        id: "lucky_charm",
        name: "<PERSON><PERSON><PERSON> de la Suerte",
        emoji: "🍀",
        points: 20,
        description: "Completar daily en 3 Lucky Days diferentes",
        category: "Especial",
        condition: (_, userStats) => {
          return userStats.luckyDaysHit >= 3;
        }
      },
      {
        id: "chameleon",
        name: "Camaleón",
        emoji: "🎭",
        points: 25,
        description: "Completar dailies en todos los días de la semana",
        category: "Especial",
        condition: (_, userStats) => {
          return userStats.daysOfWeekCompleted >= 7;
        }
      },
      {
        id: "speedster",
        name: "Velocista",
        emoji: "🏃",
        points: 30,
        description: "7 dailies en 7 días diferentes de una semana",
        category: "Especial",
        condition: (_, userStats) => {
          return userStats.perfectWeeks >= 1;
        }
      },
      {
        id: "showman",
        name: "Showman",
        emoji: "🎪",
        points: 15,
        description: "Daily más largo del mes",
        category: "Especial",
        condition: (_, userStats) => {
          return userStats.monthlyLongestDaily === true;
        }
      },
      {
        id: "word_master",
        name: "Maestro de Palabras",
        emoji: "📖",
        points: 20,
        description: "Usar la palabra del día 5 veces",
        category: "Especial",
        condition: (_, userStats) => {
          return userStats.wordOfDayUsed >= 5;
        }
      },
      {
        id: "punctual",
        name: "Puntual",
        emoji: "⏰",
        points: 15,
        description: "Completar daily a la misma hora 5 días seguidos",
        category: "Especial",
        condition: (_, userStats) => {
          return userStats.sameHourStreak >= 5;
        }
      },
      {
        id: "puchaina",
        name: "Potaxie",
        emoji: "🥑",
        points: 11,
        description: "Eres una puchaina 🥑👄🥑",
        category: "Especial",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          const pTerms = AppConfig.PUCHAINA_TERMS;
          return pTerms.some(term => text.includes(term));
        }
      },

      // === EVENT ACHIEVEMENTS ===
      {
        id: "new_year_starter",
        name: "Año Nuevo, Vida Nueva",
        emoji: "🎊",
        points: 10,
        description: "Completar daily el 1 de enero",
        category: "Evento",
        condition: (data) => {
          return data.timestamp.getMonth() === 0 && data.timestamp.getDate() === 1;
        }
      },
      {
        id: "christmas_spirit",
        name: "Espíritu Navideño",
        emoji: "🎄",
        points: 10,
        description: "Completar daily el 25 de diciembre",
        category: "Evento",
        condition: (data) => {
          return data.timestamp.getMonth() === 11 && data.timestamp.getDate() === 25;
        }
      },
      {
        id: "friday_warrior",
        name: "Guerrero del Viernes",
        emoji: "🍻",
        points: 8,
        description: "Completar daily un viernes después de las 18:00",
        category: "Evento",
        condition: (data) => {
          return data.timestamp.getDay() === 5 && data.timestamp.getHours() >= 18;
        }
      },
      {
        id: "leap_year_legend",
        name: "Leyenda del Año Bisiesto",
        emoji: "🦘",
        points: 29,
        description: "Completar daily el 29 de febrero",
        category: "Legendario",
        condition: (data) => {
          return data.timestamp.getMonth() === 1 && data.timestamp.getDate() === 29;
        }
      }
    ];
  }
}
