/**
 * User Domain Model
 * Following Single Responsibility Principle - represents user entity and its behavior
 */

class User {
  constructor(data = {}) {
    this.name = data.name || '';
    this.department = data.department || '';
    this.totalDailies = data.totalDailies || 0;
    this.points = data.points || 0;
    this.currentStreak = data.currentStreak || 0;
    this.maxStreak = data.maxStreak || 0;
    this.lastDate = data.lastDate || null;
    this.badge = data.badge || '';
    this.daysWithoutDaily = data.daysWithoutDaily || 0;
    this.achievements = data.achievements || '';
    this.shieldAvailable = data.shieldAvailable !== false; // Default true
    this.shieldLock = data.shieldLock || false;
    this.shieldLastUsed = data.shieldLastUsed || null;
  }

  /**
   * Check if user has an active streak
   * @returns {boolean}
   */
  hasActiveStreak() {
    if (!this.lastDate) return false;
    
    const now = new Date();
    const lastDaily = this.lastDate instanceof Date ? this.lastDate : new Date(this.lastDate);
    const hoursDiff = (now - lastDaily) / (1000 * 60 * 60);
    
    return hoursDiff <= AppConfig.TIME_CONFIG.HOURS_FOR_RECENT_DAILY;
  }

  /**
   * Check if user is eligible for first shield
   * @returns {boolean}
   */
  isEligibleForFirstShield() {
    return this.currentStreak >= AppConfig.SHIELD_CONFIG.FIRST_SHIELD_STREAK_REQUIRED && 
           !this.shieldAvailable && 
           !this.hasUsedShieldBefore();
  }

  /**
   * Check if user is eligible for shield regeneration
   * @returns {boolean}
   */
  isEligibleForShieldRegeneration() {
    return this.currentStreak >= AppConfig.SHIELD_CONFIG.REGENERATION_STREAK_REQUIRED && 
           !this.shieldAvailable && 
           this.hasUsedShieldBefore();
  }

  /**
   * Check if user has used shield before
   * @returns {boolean}
   */
  hasUsedShieldBefore() {
    return this.shieldLastUsed !== null && this.shieldLastUsed !== "";
  }

  /**
   * Get current badge emoji based on streak
   * @returns {string}
   */
  getCurrentBadge() {
    const milestones = AppConfig.BADGE_MILESTONES;
    for (let i = milestones.length - 1; i >= 0; i--) {
      if (this.currentStreak >= milestones[i].days) {
        return milestones[i].emoji;
      }
    }
    return "";
  }

  /**
   * Calculate days since last daily
   * @returns {number}
   */
  getDaysSinceLastDaily() {
    if (!this.lastDate) return 999;
    
    const now = new Date();
    const lastDaily = this.lastDate instanceof Date ? this.lastDate : new Date(this.lastDate);
    return Math.floor((now - lastDaily) / (1000 * 60 * 60 * 24));
  }

  /**
   * Update streak based on new daily
   * @param {Date} newDailyDate - Date of new daily
   * @returns {Object} Updated streak information
   */
  updateStreak(newDailyDate) {
    if (!this.lastDate) {
      this.currentStreak = 1;
      this.maxStreak = Math.max(this.maxStreak, 1);
      this.lastDate = newDailyDate;
      return { newStreak: 1, streakBroken: false };
    }

    const lastDaily = this.lastDate instanceof Date ? this.lastDate : new Date(this.lastDate);
    const daysDiff = Math.floor((newDailyDate - lastDaily) / (1000 * 60 * 60 * 24));

    if (daysDiff === 1) {
      // Consecutive day
      this.currentStreak += 1;
      this.maxStreak = Math.max(this.maxStreak, this.currentStreak);
      this.lastDate = newDailyDate;
      return { newStreak: this.currentStreak, streakBroken: false };
    } else if (daysDiff > 1) {
      // Streak broken
      const oldStreak = this.currentStreak;
      this.currentStreak = 1;
      this.lastDate = newDailyDate;
      return { newStreak: 1, streakBroken: true, previousStreak: oldStreak };
    } else {
      // Same day or earlier (no streak change)
      this.lastDate = newDailyDate;
      return { newStreak: this.currentStreak, streakBroken: false };
    }
  }

  /**
   * Add points to user
   * @param {number} points - Points to add
   */
  addPoints(points) {
    this.points += points;
  }

  /**
   * Increment total dailies
   */
  incrementDailies() {
    this.totalDailies += 1;
  }

  /**
   * Convert to array format for Google Sheets
   * @returns {Array}
   */
  toSheetRow() {
    return [
      this.name,
      this.department,
      this.totalDailies,
      this.points,
      this.currentStreak,
      this.maxStreak,
      this.lastDate instanceof Date ? this.lastDate.getTime() : this.lastDate,
      this.badge,
      this.daysWithoutDaily,
      this.achievements,
      this.shieldAvailable,
      this.shieldLock,
      this.shieldLastUsed
    ];
  }

  /**
   * Create User from sheet row
   * @param {Array} row - Sheet row data
   * @returns {User}
   */
  static fromSheetRow(row) {
    return new User({
      name: row[0] || '',
      department: row[1] || '',
      totalDailies: row[2] || 0,
      points: row[3] || 0,
      currentStreak: row[4] || 0,
      maxStreak: row[5] || 0,
      lastDate: row[6] || null,
      badge: row[7] || '',
      daysWithoutDaily: row[8] || 0,
      achievements: row[9] || '',
      shieldAvailable: row[10] !== false,
      shieldLock: row[11] || false,
      shieldLastUsed: row[12] || null
    });
  }
}
