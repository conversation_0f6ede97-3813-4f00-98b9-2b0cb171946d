/**
 * Admin Controller
 * Following Single Responsibility Principle - handles administrative operations
 */

class AdminController {
  constructor(userService, achievementService, shieldService, pointsService, gameConfigRepository) {
    this.userService = userService;
    this.achievementService = achievementService;
    this.shieldService = shieldService;
    this.pointsService = pointsService;
    this.gameConfigRepository = gameConfigRepository;
  }

  /**
   * Get comprehensive system statistics
   * @returns {Object}
   */
  getSystemStatistics() {
    const overallStats = this.userService.getOverallStatistics();
    const achievementStats = this.achievementService.getAchievementStatistics();
    const shieldStats = this.shieldService.getShieldStatistics();
    
    return {
      users: overallStats,
      achievements: {
        totalAchievements: Object.keys(achievementStats).length,
        rarestAchievements: this.achievementService.getRarestAchievements(3),
        popularAchievements: this.achievementService.getMostPopularAchievements(3)
      },
      shields: shieldStats,
      system: {
        lastUpdate: new Date(),
        version: "2.0.0-SOLID"
      }
    };
  }

  /**
   * Recalculate points for all users
   * @returns {Object}
   */
  recalculateAllPoints() {
    const users = this.userService.userRepository.getAllUsers();
    let successCount = 0;
    let errorCount = 0;
    
    for (const user of users) {
      try {
        const success = this.pointsService.recalculateUserPoints(user.name);
        if (success) {
          successCount++;
        } else {
          errorCount++;
        }
      } catch (error) {
        console.log(`❌ Error recalculating points for ${user.name}: ${error.message}`);
        errorCount++;
      }
    }
    
    return {
      totalUsers: users.length,
      successCount: successCount,
      errorCount: errorCount
    };
  }

  /**
   * Reset user data
   * @param {string} userName - User name
   * @param {Object} options - Reset options
   * @returns {boolean}
   */
  resetUserData(userName, options = {}) {
    const user = this.userService.userRepository.getByName(userName);
    if (!user) return false;
    
    try {
      if (options.resetPoints) {
        user.points = 0;
      }
      
      if (options.resetStreak) {
        user.currentStreak = 0;
        user.maxStreak = 0;
        user.badge = '';
      }
      
      if (options.resetDailies) {
        user.totalDailies = 0;
      }
      
      if (options.resetShield) {
        this.shieldService.resetUserShield(userName);
      }
      
      if (options.resetAchievements) {
        // Note: This would require achievement deletion functionality
        console.log(`⚠️ Achievement reset not implemented for safety`);
      }
      
      return this.userService.userRepository.saveUser(user);
    } catch (error) {
      console.log(`❌ Error resetting user data for ${userName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Grant achievement to user (admin function)
   * @param {string} userName - User name
   * @param {string} achievementId - Achievement ID
   * @returns {boolean}
   */
  grantAchievementToUser(userName, achievementId) {
    return this.achievementService.grantAchievement(userName, achievementId);
  }

  /**
   * Grant shield to user (admin function)
   * @param {string} userName - User name
   * @returns {boolean}
   */
  grantShieldToUser(userName) {
    return this.shieldService.forceGrantShield(userName);
  }

  /**
   * Update game configuration
   * @param {string} ruleName - Rule name
   * @param {number} value - New value
   * @param {boolean} active - Whether rule is active
   * @returns {boolean}
   */
  updateGameConfig(ruleName, value, active = true) {
    return this.gameConfigRepository.updateRule(ruleName, value, active);
  }

  /**
   * Toggle game rule
   * @param {string} ruleName - Rule name
   * @returns {boolean}
   */
  toggleGameRule(ruleName) {
    return this.gameConfigRepository.toggleRule(ruleName);
  }

  /**
   * Get game configuration
   * @returns {GameConfig}
   */
  getGameConfiguration() {
    return this.gameConfigRepository.getGameConfig();
  }

  /**
   * Reset game configuration to defaults
   * @returns {boolean}
   */
  resetGameConfiguration() {
    return this.gameConfigRepository.resetToDefault();
  }

  /**
   * Export user data
   * @param {string} userName - User name (optional, if null exports all)
   * @returns {Object}
   */
  exportUserData(userName = null) {
    if (userName) {
      const user = this.userService.userRepository.getByName(userName);
      const achievements = this.achievementService.getUserAchievements(userName);
      const pointsSummary = this.pointsService.getUserPointsSummary(userName);
      
      return {
        user: user,
        achievements: achievements,
        pointsSummary: pointsSummary
      };
    } else {
      const users = this.userService.userRepository.getAllUsers();
      const allAchievements = {};
      
      users.forEach(user => {
        allAchievements[user.name] = this.achievementService.getUserAchievements(user.name);
      });
      
      return {
        users: users,
        achievements: allAchievements,
        systemStats: this.getSystemStatistics()
      };
    }
  }

  /**
   * Process shield protection check (scheduled task)
   * @returns {Object}
   */
  processShieldProtection() {
    try {
      const protectedUsers = this.shieldService.processShieldProtection();
      const newShields = this.shieldService.checkAndGrantShields();
      
      return {
        success: true,
        protectedUsers: protectedUsers.length,
        newShields: newShields.length,
        details: {
          protected: protectedUsers.map(user => user.name),
          newShields: newShields.map(shield => shield.user.name)
        }
      };
    } catch (error) {
      console.log(`❌ Error processing shield protection: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get system health check
   * @returns {Object}
   */
  getSystemHealth() {
    try {
      const users = this.userService.userRepository.getAllUsers();
      const gameConfig = this.gameConfigRepository.getGameConfig();
      const achievements = this.achievementService.achievements;
      
      return {
        status: 'healthy',
        components: {
          users: { status: 'ok', count: users.length },
          achievements: { status: 'ok', count: achievements.length },
          gameConfig: { status: 'ok', valid: gameConfig.validate().isValid },
          repositories: { status: 'ok' },
          services: { status: 'ok' }
        },
        lastCheck: new Date()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        lastCheck: new Date()
      };
    }
  }

  /**
   * Backup system data
   * @returns {Object}
   */
  backupSystemData() {
    try {
      const userData = this.userService.userRepository.getAll();
      const achievementData = this.achievementService.achievementRepository.getAll();
      const gameConfigData = this.gameConfigRepository.backupConfiguration();
      
      return {
        success: true,
        backup: {
          users: userData,
          achievements: achievementData,
          gameConfig: gameConfigData,
          timestamp: new Date()
        }
      };
    } catch (error) {
      console.log(`❌ Error creating backup: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
