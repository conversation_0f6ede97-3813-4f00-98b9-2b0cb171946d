/**
 * Points Calculation Service
 * Following Single Responsibility Principle - only handles points calculation logic
 */

class PointsService {
  constructor(gameConfigRepository, dailyRepository) {
    this.gameConfigRepository = gameConfigRepository;
    this.dailyRepository = dailyRepository;
  }

  /**
   * Calculate points for a daily submission
   * @param {Daily} daily - Daily object
   * @param {User} user - User object
   * @returns {Object} Points calculation result
   */
  calculateDailyPoints(daily, user) {
    const gameConfig = this.gameConfigRepository.getGameConfig();
    const specialConditions = this.evaluateSpecialConditions(daily, user);
    
    // Base points
    let points = gameConfig.calculateBasePoints(daily);
    
    // Bonus points
    const bonusPoints = gameConfig.calculateBonusPoints(
      daily,
      specialConditions.isLuckyDay,
      specialConditions.usedWordOfDay,
      specialConditions.isProjectWork
    );
    points += bonusPoints;
    
    // Apply multipliers
    const finalPoints = gameConfig.applyMultipliers(
      points,
      daily,
      specialConditions.projectFocusLevel
    );
    
    return {
      basePoints: gameConfig.calculateBasePoints(daily),
      bonusPoints: bonusPoints,
      finalPoints: finalPoints,
      breakdown: this.getPointsBreakdown(daily, gameConfig, specialConditions),
      specialConditions: specialConditions
    };
  }

  /**
   * Evaluate special conditions for points calculation
   * @param {Daily} daily - Daily object
   * @param {User} user - User object
   * @returns {Object}
   */
  evaluateSpecialConditions(daily, user) {
    return {
      isLuckyDay: this.isLuckyDay(daily.timestamp),
      usedWordOfDay: this.usedWordOfDay(daily),
      isProjectWork: this.isProjectWork(daily),
      projectFocusLevel: this.calculateProjectFocusLevel(daily),
      isEarlyBird: daily.isEarlySubmission(),
      isWeekendWarrior: daily.isWeekendSubmission(),
      isDetailed: daily.isDetailed()
    };
  }

  /**
   * Check if it's a lucky day
   * @param {Date} date - Date to check
   * @returns {boolean}
   */
  isLuckyDay(date) {
    const day = date.getDate();
    const month = date.getMonth() + 1;
    
    // Lucky days: 7, 13, 21 of any month, or any day in July (month 7)
    return day === 7 || day === 13 || day === 21 || month === 7;
  }

  /**
   * Check if daily uses word of the day
   * @param {Daily} daily - Daily object
   * @returns {boolean}
   */
  usedWordOfDay(daily) {
    const wordOfDay = this.getWordOfDay(daily.timestamp);
    if (!wordOfDay) return false;
    
    return daily.containsWord(wordOfDay);
  }

  /**
   * Get word of the day
   * @param {Date} date - Date
   * @returns {string|null}
   */
  getWordOfDay(date) {
    try {
      const configSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(AppConfig.SHEET_NAMES.CONFIG);
      if (!configSheet) return null;
      
      const data = configSheet.getDataRange().getValues();
      const wordRow = data.find(row => row[0] === "PalabraDia");
      
      return wordRow ? wordRow[1] : null;
    } catch (error) {
      console.log(`❌ Error getting word of day: ${error.message}`);
      return null;
    }
  }

  /**
   * Check if daily mentions project work
   * @param {Daily} daily - Daily object
   * @returns {boolean}
   */
  isProjectWork(daily) {
    const projectKeywords = AppConfig.PROJECT_KEYWORDS;
    return daily.containsAnyTerm(projectKeywords);
  }

  /**
   * Calculate project focus level (0-1)
   * @param {Daily} daily - Daily object
   * @returns {number}
   */
  calculateProjectFocusLevel(daily) {
    const text = daily.getFullText();
    const totalWords = text.split(/\s+/).length;
    
    if (totalWords === 0) return 0;
    
    const projectKeywords = AppConfig.PROJECT_KEYWORDS;
    let projectWords = 0;
    
    projectKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      const matches = text.match(regex);
      if (matches) {
        projectWords += matches.length;
      }
    });
    
    return Math.min(projectWords / totalWords, 1);
  }

  /**
   * Get detailed points breakdown
   * @param {Daily} daily - Daily object
   * @param {GameConfig} gameConfig - Game configuration
   * @param {Object} specialConditions - Special conditions
   * @returns {Array}
   */
  getPointsBreakdown(daily, gameConfig, specialConditions) {
    const breakdown = [];
    
    // Base points
    breakdown.push({
      source: "Base",
      points: gameConfig.puntoBase,
      description: "Punto base por completar daily"
    });
    
    // Early bird bonus
    if (specialConditions.isEarlyBird) {
      breakdown.push({
        source: "Early Bird",
        points: gameConfig.bonusEarlyBird,
        description: "Bonus por daily antes de 9am"
      });
    }
    
    // Weekend warrior bonus
    if (specialConditions.isWeekendWarrior) {
      breakdown.push({
        source: "Weekend Warrior",
        points: gameConfig.bonusWeekendWarrior,
        description: "Bonus por daily en fin de semana"
      });
    }
    
    // Detailed daily bonus
    if (specialConditions.isDetailed) {
      breakdown.push({
        source: "Detailed",
        points: gameConfig.bonusDetailed,
        description: "Bonus por daily detallado (+200 chars)"
      });
    }
    
    // Lucky day bonus
    if (specialConditions.isLuckyDay) {
      breakdown.push({
        source: "Lucky Day",
        points: gameConfig.bonusLucky,
        description: "Bonus en Lucky Day"
      });
    }
    
    // Word of day bonus
    if (specialConditions.usedWordOfDay) {
      breakdown.push({
        source: "Word of Day",
        points: gameConfig.bonusWord,
        description: "Bonus por usar palabra del día"
      });
    }
    
    // Project work bonus
    if (specialConditions.isProjectWork) {
      breakdown.push({
        source: "Project Work",
        points: gameConfig.bonusProjectWork,
        description: "Bonus por mencionar trabajo del proyecto"
      });
    }
    
    return breakdown;
  }

  /**
   * Get points summary for user
   * @param {string} userName - User name
   * @returns {Object}
   */
  getUserPointsSummary(userName) {
    const user = this.userRepository.getByName(userName);
    if (!user) return null;
    
    const userDailies = this.dailyRepository.getUserDailies(userName);
    let totalCalculatedPoints = 0;
    let bonusPointsTotal = 0;
    let basePointsTotal = 0;
    
    userDailies.forEach(daily => {
      const pointsResult = this.calculateDailyPoints(daily, user);
      totalCalculatedPoints += pointsResult.finalPoints;
      bonusPointsTotal += pointsResult.bonusPoints;
      basePointsTotal += pointsResult.basePoints;
    });
    
    return {
      currentPoints: user.points,
      calculatedPoints: totalCalculatedPoints,
      basePoints: basePointsTotal,
      bonusPoints: bonusPointsTotal,
      achievementPoints: user.points - totalCalculatedPoints, // Approximate
      dailiesCount: userDailies.length
    };
  }

  /**
   * Recalculate all user points (admin function)
   * @param {string} userName - User name
   * @returns {boolean}
   */
  recalculateUserPoints(userName) {
    const user = this.userRepository.getByName(userName);
    if (!user) return false;
    
    const userDailies = this.dailyRepository.getUserDailies(userName);
    let totalPoints = 0;
    
    userDailies.forEach(daily => {
      const pointsResult = this.calculateDailyPoints(daily, user);
      totalPoints += pointsResult.finalPoints;
    });
    
    // Add achievement points
    // Note: This would need achievement service injection for full calculation
    
    user.points = totalPoints;
    return this.userRepository.saveUser(user);
  }
}
