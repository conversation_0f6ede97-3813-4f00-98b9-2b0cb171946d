/**
 * Discord Notification Service
 * Following Single Responsibility Principle - only handles Discord notifications
 * Following Dependency Inversion Principle - implements INotificationService interface
 */

class DiscordService extends INotificationService {
  constructor(webhookUrl = null) {
    super();
    this.webhookUrl = webhookUrl || AppConfig.DISCORD_WEBHOOK_URL;
  }

  /**
   * Send a simple message to Discord
   * @param {string} message - Message to send
   * @returns {Promise<boolean>}
   */
  async sendMessage(message) {
    try {
      const payload = {
        content: message,
        username: "Daily Bot",
        avatar_url: "https://cdn-icons-png.flaticon.com/512/2111/2111615.png"
      };

      const options = {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        payload: JSON.stringify(payload)
      };

      const response = UrlFetchApp.fetch(this.webhookUrl, options);
      return response.getResponseCode() === 204;
    } catch (error) {
      console.log(`❌ Error sending Discord message: ${error.message}`);
      return false;
    }
  }

  /**
   * Send formatted daily report to Discord
   * @param {Daily} daily - Daily object
   * @param {User} user - User object
   * @param {number} pointsEarned - Points earned for this daily
   * @returns {Promise<boolean>}
   */
  async sendDailyReport(daily, user, pointsEarned = 0) {
    try {
      const embed = this.createDailyEmbed(daily, user, pointsEarned);
      
      const payload = {
        username: "Daily Bot",
        avatar_url: "https://cdn-icons-png.flaticon.com/512/2111/2111615.png",
        embeds: [embed]
      };

      const options = {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        payload: JSON.stringify(payload)
      };

      const response = UrlFetchApp.fetch(this.webhookUrl, options);
      return response.getResponseCode() === 204;
    } catch (error) {
      console.log(`❌ Error sending daily report: ${error.message}`);
      return false;
    }
  }

  /**
   * Send achievement notification to Discord
   * @param {string} userName - User name
   * @param {Array<Achievement>} achievements - Array of achievements
   * @returns {Promise<boolean>}
   */
  async sendAchievementNotification(userName, achievements) {
    if (!achievements || achievements.length === 0) return true;

    try {
      const embed = this.createAchievementEmbed(userName, achievements);
      
      const payload = {
        username: "Achievement Bot",
        avatar_url: "https://cdn-icons-png.flaticon.com/512/2553/2553642.png",
        embeds: [embed]
      };

      const options = {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        payload: JSON.stringify(payload)
      };

      const response = UrlFetchApp.fetch(this.webhookUrl, options);
      return response.getResponseCode() === 204;
    } catch (error) {
      console.log(`❌ Error sending achievement notification: ${error.message}`);
      return false;
    }
  }

  /**
   * Send shield notification to Discord
   * @param {string} userName - User name
   * @param {string} type - Type of shield notification
   * @param {Object} details - Additional details
   * @returns {Promise<boolean>}
   */
  async sendShieldNotification(userName, type, details = {}) {
    try {
      let message = "";
      let color = 0x00ff00; // Green

      switch (type) {
        case 'granted':
          message = `🛡️ **${userName}** ha ganado un escudo protector! Racha de ${details.streak} días.`;
          color = 0x00ff00;
          break;
        case 'consumed':
          message = `💥 **${userName}** ha usado su escudo protector para mantener su racha de ${details.streak} días!`;
          color = 0xff9900;
          break;
        case 'restored':
          message = `✨ **${userName}** ha regenerado su escudo protector! Nueva racha de ${details.streak} días.`;
          color = 0x0099ff;
          break;
        default:
          message = `🛡️ **${userName}** - Evento de escudo: ${type}`;
      }

      const embed = {
        title: "🛡️ Sistema de Escudos",
        description: message,
        color: color,
        timestamp: new Date().toISOString(),
        footer: {
          text: "Daily Gamification System"
        }
      };

      const payload = {
        username: "Shield Bot",
        avatar_url: "https://cdn-icons-png.flaticon.com/512/1087/1087815.png",
        embeds: [embed]
      };

      const options = {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        payload: JSON.stringify(payload)
      };

      const response = UrlFetchApp.fetch(this.webhookUrl, options);
      return response.getResponseCode() === 204;
    } catch (error) {
      console.log(`❌ Error sending shield notification: ${error.message}`);
      return false;
    }
  }

  /**
   * Create daily report embed
   * @param {Daily} daily - Daily object
   * @param {User} user - User object
   * @param {number} pointsEarned - Points earned
   * @returns {Object}
   */
  createDailyEmbed(daily, user, pointsEarned) {
    const fields = [];

    if (daily.yesterday) {
      fields.push({
        name: "🔙 Ayer",
        value: daily.yesterday.substring(0, 1024),
        inline: false
      });
    }

    if (daily.today) {
      fields.push({
        name: "📅 Hoy",
        value: daily.today.substring(0, 1024),
        inline: false
      });
    }

    if (daily.impediments) {
      fields.push({
        name: "🚧 Impedimentos",
        value: daily.impediments.substring(0, 1024),
        inline: false
      });
    }

    // Stats field
    const statsText = `**Puntos:** ${user.points} (+${pointsEarned})\n**Racha:** ${user.currentStreak} días ${user.badge}\n**Total Dailies:** ${user.totalDailies}`;
    fields.push({
      name: "📊 Estadísticas",
      value: statsText,
      inline: true
    });

    return {
      title: `📝 Daily de ${daily.userName}`,
      color: 0x00ff00,
      fields: fields,
      timestamp: daily.timestamp.toISOString(),
      footer: {
        text: `Departamento: ${user.department || 'Sin departamento'}`
      }
    };
  }

  /**
   * Create achievement embed
   * @param {string} userName - User name
   * @param {Array<Achievement>} achievements - Achievements array
   * @returns {Object}
   */
  createAchievementEmbed(userName, achievements) {
    const totalPoints = achievements.reduce((sum, ach) => sum + ach.points, 0);
    
    const achievementList = achievements.map(ach => 
      `${ach.emoji} **${ach.name}** - ${ach.description} (+${ach.points} pts)`
    ).join('\n');

    return {
      title: "🏆 ¡Nuevos Logros Desbloqueados!",
      description: `**${userName}** ha conseguido ${achievements.length} nuevo(s) logro(s):\n\n${achievementList}`,
      color: 0xffd700,
      fields: [
        {
          name: "💰 Puntos Ganados",
          value: `+${totalPoints} puntos`,
          inline: true
        }
      ],
      timestamp: new Date().toISOString(),
      footer: {
        text: "Daily Gamification System"
      }
    };
  }

  /**
   * Test Discord connection
   * @returns {Promise<boolean>}
   */
  async testConnection() {
    return await this.sendMessage("🧪 Test de conexión - Sistema funcionando correctamente!");
  }
}
