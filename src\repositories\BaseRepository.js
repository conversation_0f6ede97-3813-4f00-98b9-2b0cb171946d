/**
 * Base Repository
 * Following Dependency Inversion Principle - abstracts Google Sheets operations
 */

class BaseRepository extends IRepository {
  constructor(sheetName, headers = []) {
    super();
    this.sheetName = sheetName;
    this.headers = headers;
    this._sheet = null;
  }

  /**
   * Get or create the sheet
   * @returns {GoogleAppsScript.Spreadsheet.Sheet}
   */
  getSheet() {
    if (!this._sheet) {
      this._sheet = this.getOrCreateSheet(this.sheetName, this.headers);
    }
    return this._sheet;
  }

  /**
   * Get or create sheet with headers
   * @param {string} name - Sheet name
   * @param {Array} header - Header row
   * @returns {GoogleAppsScript.Spreadsheet.Sheet}
   */
  getOrCreateSheet(name, header) {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let sh = ss.getSheetByName(name);
    if (!sh) sh = ss.insertSheet(name);
    if (header && header.length > 0 && sh.getLastRow() === 0) {
      sh.appendRow(header);
    }
    return sh;
  }

  /**
   * Get all data from sheet
   * @returns {Array}
   */
  getAll() {
    const sheet = this.getSheet();
    if (sheet.getLastRow() <= 1) return [];
    
    const data = sheet.getDataRange().getValues();
    return data.slice(1); // Remove header
  }

  /**
   * Get data by condition
   * @param {Function} predicate - Condition function
   * @returns {Array}
   */
  getWhere(predicate) {
    const allData = this.getAll();
    return allData.filter(predicate);
  }

  /**
   * Get single record by condition
   * @param {Function} predicate - Condition function
   * @returns {Array|null}
   */
  getFirst(predicate) {
    const results = this.getWhere(predicate);
    return results.length > 0 ? results[0] : null;
  }

  /**
   * Add new row to sheet
   * @param {Array} rowData - Row data
   * @returns {boolean}
   */
  create(rowData) {
    try {
      const sheet = this.getSheet();
      sheet.appendRow(rowData);
      return true;
    } catch (error) {
      console.log(`❌ Error creating record in ${this.sheetName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Update row by index
   * @param {number} rowIndex - 1-based row index
   * @param {Array} rowData - New row data
   * @returns {boolean}
   */
  updateByIndex(rowIndex, rowData) {
    try {
      const sheet = this.getSheet();
      sheet.getRange(rowIndex, 1, 1, rowData.length).setValues([rowData]);
      return true;
    } catch (error) {
      console.log(`❌ Error updating record in ${this.sheetName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Update specific cell
   * @param {number} row - 1-based row index
   * @param {number} col - 1-based column index
   * @param {*} value - New value
   * @returns {boolean}
   */
  updateCell(row, col, value) {
    try {
      const sheet = this.getSheet();
      sheet.getRange(row, col).setValue(value);
      return true;
    } catch (error) {
      console.log(`❌ Error updating cell in ${this.sheetName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Update range of cells
   * @param {number} startRow - 1-based start row
   * @param {number} startCol - 1-based start column
   * @param {Array} values - 2D array of values
   * @returns {boolean}
   */
  updateRange(startRow, startCol, values) {
    try {
      const sheet = this.getSheet();
      const numRows = values.length;
      const numCols = values[0] ? values[0].length : 0;
      sheet.getRange(startRow, startCol, numRows, numCols).setValues(values);
      return true;
    } catch (error) {
      console.log(`❌ Error updating range in ${this.sheetName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Delete row by index
   * @param {number} rowIndex - 1-based row index
   * @returns {boolean}
   */
  deleteByIndex(rowIndex) {
    try {
      const sheet = this.getSheet();
      sheet.deleteRow(rowIndex);
      return true;
    } catch (error) {
      console.log(`❌ Error deleting record in ${this.sheetName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Clear all content except headers
   * @returns {boolean}
   */
  clearContent() {
    try {
      const sheet = this.getSheet();
      if (sheet.getLastRow() > 1) {
        sheet.getRange(2, 1, sheet.getLastRow() - 1, sheet.getLastColumn()).clearContent();
      }
      return true;
    } catch (error) {
      console.log(`❌ Error clearing content in ${this.sheetName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Get row count (excluding header)
   * @returns {number}
   */
  getRowCount() {
    const sheet = this.getSheet();
    return Math.max(0, sheet.getLastRow() - 1);
  }

  /**
   * Find row index by condition
   * @param {Function} predicate - Condition function
   * @returns {number} 1-based row index or -1 if not found
   */
  findRowIndex(predicate) {
    const allData = this.getAll();
    for (let i = 0; i < allData.length; i++) {
      if (predicate(allData[i])) {
        return i + 2; // +2 because: +1 for 1-based indexing, +1 for header row
      }
    }
    return -1;
  }
}
