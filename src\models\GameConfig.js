/**
 * Game Configuration Domain Model
 * Following Single Responsibility Principle - represents game configuration and its behavior
 */

class GameConfig {
  constructor(data = {}) {
    this.puntoBase = data.PuntoBase || 1;
    this.bonusLucky = data.BonusLucky || 1;
    this.bonusWord = data.BonusWord || 1;
    this.multiplierWeekend = data.MultiplierWeekend || 1;
    this.bonusEarlyBird = data.BonusEarlyBird || 2;
    this.bonusWeekendWarrior = data.BonusWeekendWarrior || 3;
    this.bonusDetailed = data.BonusDetailed || 1;
    this.bonusProjectWork = data.BonusProjectWork || 3;
    this.multiplierProjectFocus = data.MultiplierProjectFocus || 1.5;
  }

  /**
   * Calculate base points for a daily
   * @param {Daily} daily - Daily object
   * @returns {number}
   */
  calculateBasePoints(daily) {
    let points = this.puntoBase;

    // Early bird bonus
    if (daily.isEarlySubmission()) {
      points += this.bonusEarlyBird;
    }

    // Weekend warrior bonus
    if (daily.isWeekendSubmission()) {
      points += this.bonusWeekendWarrior;
    }

    // Detailed daily bonus
    if (daily.isDetailed()) {
      points += this.bonusDetailed;
    }

    return points;
  }

  /**
   * Calculate bonus points for special conditions
   * @param {Daily} daily - Daily object
   * @param {boolean} isLuckyDay - Whether it's a lucky day
   * @param {boolean} usedWordOfDay - Whether word of day was used
   * @param {boolean} isProjectWork - Whether daily mentions project work
   * @returns {number}
   */
  calculateBonusPoints(daily, isLuckyDay, usedWordOfDay, isProjectWork) {
    let bonus = 0;

    if (isLuckyDay) {
      bonus += this.bonusLucky;
    }

    if (usedWordOfDay) {
      bonus += this.bonusWord;
    }

    if (isProjectWork) {
      bonus += this.bonusProjectWork;
    }

    return bonus;
  }

  /**
   * Apply multipliers to points
   * @param {number} points - Base points
   * @param {Daily} daily - Daily object
   * @param {number} projectFocusLevel - Project focus level (0-1)
   * @returns {number}
   */
  applyMultipliers(points, daily, projectFocusLevel = 0) {
    let finalPoints = points;

    // Weekend multiplier
    if (daily.isWeekendSubmission() && this.multiplierWeekend > 1) {
      finalPoints = Math.floor(finalPoints * this.multiplierWeekend);
    }

    // Project focus multiplier
    if (projectFocusLevel >= 0.8 && this.multiplierProjectFocus > 1) {
      finalPoints = Math.floor(finalPoints * this.multiplierProjectFocus);
    }

    return finalPoints;
  }

  /**
   * Get configuration as object for backward compatibility
   * @returns {Object}
   */
  toObject() {
    return {
      PuntoBase: this.puntoBase,
      BonusLucky: this.bonusLucky,
      BonusWord: this.bonusWord,
      MultiplierWeekend: this.multiplierWeekend,
      BonusEarlyBird: this.bonusEarlyBird,
      BonusWeekendWarrior: this.bonusWeekendWarrior,
      BonusDetailed: this.bonusDetailed,
      BonusProjectWork: this.bonusProjectWork,
      MultiplierProjectFocus: this.multiplierProjectFocus
    };
  }

  /**
   * Create GameConfig from sheet data
   * @param {Array} sheetData - Sheet data rows
   * @returns {GameConfig}
   */
  static fromSheetData(sheetData) {
    const config = {};
    
    for (let i = 1; i < sheetData.length; i++) {
      const [rule, value, active] = sheetData[i];
      if (active) {
        config[rule] = Number(value) || 0;
      }
    }
    
    return new GameConfig(config);
  }

  /**
   * Get default configuration
   * @returns {GameConfig}
   */
  static getDefault() {
    return new GameConfig();
  }

  /**
   * Validate configuration
   * @returns {Object} Validation result
   */
  validate() {
    const errors = [];
    
    if (this.puntoBase < 0) {
      errors.push('Base points cannot be negative');
    }
    
    if (this.multiplierWeekend < 0) {
      errors.push('Weekend multiplier cannot be negative');
    }
    
    if (this.multiplierProjectFocus < 0) {
      errors.push('Project focus multiplier cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}
