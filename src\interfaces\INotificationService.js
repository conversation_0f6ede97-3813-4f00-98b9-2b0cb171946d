/**
 * Notification Service Interface
 * Following Interface Segregation Principle - defines contract for notification services
 */

class INotificationService {
  /**
   * Send a simple message
   * @param {string} message - Message to send
   * @returns {Promise<boolean>} Success status
   */
  async sendMessage(message) {
    throw new Error("Method 'sendMessage' must be implemented");
  }

  /**
   * Send a formatted daily report
   * @param {Object} dailyData - Daily report data
   * @param {Object} userStats - User statistics
   * @returns {Promise<boolean>} Success status
   */
  async sendDailyReport(dailyData, userStats) {
    throw new Error("Method 'sendDailyReport' must be implemented");
  }

  /**
   * Send achievement notification
   * @param {string} userName - User name
   * @param {Array} achievements - Array of achievements
   * @returns {Promise<boolean>} Success status
   */
  async sendAchievementNotification(userName, achievements) {
    throw new Error("Method 'sendAchievementNotification' must be implemented");
  }

  /**
   * Send shield notification
   * @param {string} userName - User name
   * @param {string} type - Type of shield notification (granted, consumed, restored)
   * @param {Object} details - Additional details
   * @returns {Promise<boolean>} Success status
   */
  async sendShieldNotification(userName, type, details) {
    throw new Error("Method 'sendShieldNotification' must be implemented");
  }
}
