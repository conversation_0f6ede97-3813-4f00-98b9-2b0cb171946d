/**
 * Validation Utilities
 * Following Single Responsibility Principle - only handles validation operations
 */

class ValidationUtils {
  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean}
   */
  static isValidEmail(email) {
    if (!email || typeof email !== 'string') return false;
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate URL format
   * @param {string} url - URL to validate
   * @returns {boolean}
   */
  static isValidUrl(url) {
    if (!url || typeof url !== 'string') return false;
    
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate user name
   * @param {string} userName - User name to validate
   * @returns {Object}
   */
  static validateUserName(userName) {
    const errors = [];
    
    if (!userName || typeof userName !== 'string') {
      errors.push('User name is required');
      return { isValid: false, errors };
    }
    
    const trimmedName = userName.trim();
    
    if (trimmedName.length === 0) {
      errors.push('User name cannot be empty');
    }
    
    if (trimmedName.length < 2) {
      errors.push('User name must be at least 2 characters long');
    }
    
    if (trimmedName.length > 50) {
      errors.push('User name cannot exceed 50 characters');
    }
    
    // Check for invalid characters
    const invalidChars = /[<>\"'&]/;
    if (invalidChars.test(trimmedName)) {
      errors.push('User name contains invalid characters');
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      cleanName: trimmedName
    };
  }

  /**
   * Validate daily content
   * @param {Object} dailyData - Daily data to validate
   * @returns {Object}
   */
  static validateDailyContent(dailyData) {
    const errors = [];
    
    if (!dailyData) {
      errors.push('Daily data is required');
      return { isValid: false, errors };
    }

    // Validate user name
    const userNameValidation = this.validateUserName(dailyData.userName);
    if (!userNameValidation.isValid) {
      errors.push(...userNameValidation.errors);
    }

    // Validate today section (required)
    if (!dailyData.today || typeof dailyData.today !== 'string' || dailyData.today.trim().length === 0) {
      errors.push('Today section is required');
    }

    // Validate content length
    const totalContent = (dailyData.yesterday || '') + (dailyData.today || '') + (dailyData.impediments || '');
    if (totalContent.length > 5000) {
      errors.push('Daily content is too long (max 5000 characters)');
    }

    // Validate timestamp
    if (!dailyData.timestamp || !(dailyData.timestamp instanceof Date) || isNaN(dailyData.timestamp.getTime())) {
      errors.push('Valid timestamp is required');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * Validate points value
   * @param {*} points - Points value to validate
   * @returns {Object}
   */
  static validatePoints(points) {
    const errors = [];
    
    if (points === null || points === undefined) {
      errors.push('Points value is required');
      return { isValid: false, errors };
    }
    
    const numericPoints = Number(points);
    
    if (isNaN(numericPoints)) {
      errors.push('Points must be a valid number');
    } else {
      if (numericPoints < 0) {
        errors.push('Points cannot be negative');
      }
      
      if (numericPoints > 10000) {
        errors.push('Points value is too high (max 10000)');
      }
      
      if (!Number.isInteger(numericPoints)) {
        errors.push('Points must be a whole number');
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      value: numericPoints
    };
  }

  /**
   * Validate streak value
   * @param {*} streak - Streak value to validate
   * @returns {Object}
   */
  static validateStreak(streak) {
    const errors = [];
    
    if (streak === null || streak === undefined) {
      errors.push('Streak value is required');
      return { isValid: false, errors };
    }
    
    const numericStreak = Number(streak);
    
    if (isNaN(numericStreak)) {
      errors.push('Streak must be a valid number');
    } else {
      if (numericStreak < 0) {
        errors.push('Streak cannot be negative');
      }
      
      if (numericStreak > 1000) {
        errors.push('Streak value is too high (max 1000)');
      }
      
      if (!Number.isInteger(numericStreak)) {
        errors.push('Streak must be a whole number');
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      value: numericStreak
    };
  }

  /**
   * Validate achievement ID
   * @param {string} achievementId - Achievement ID to validate
   * @returns {Object}
   */
  static validateAchievementId(achievementId) {
    const errors = [];
    
    if (!achievementId || typeof achievementId !== 'string') {
      errors.push('Achievement ID is required');
      return { isValid: false, errors };
    }
    
    const trimmedId = achievementId.trim();
    
    if (trimmedId.length === 0) {
      errors.push('Achievement ID cannot be empty');
    }
    
    if (trimmedId.length > 100) {
      errors.push('Achievement ID is too long (max 100 characters)');
    }
    
    // Check for valid characters (alphanumeric, underscore, hyphen)
    const validChars = /^[a-zA-Z0-9_-]+$/;
    if (!validChars.test(trimmedId)) {
      errors.push('Achievement ID can only contain letters, numbers, underscores, and hyphens');
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      cleanId: trimmedId
    };
  }

  /**
   * Validate department name
   * @param {string} department - Department name to validate
   * @returns {Object}
   */
  static validateDepartment(department) {
    const errors = [];
    
    if (!department || typeof department !== 'string') {
      // Department is optional, so empty is valid
      return { isValid: true, errors: [], cleanDepartment: '' };
    }
    
    const trimmedDept = department.trim();
    
    if (trimmedDept.length > 100) {
      errors.push('Department name is too long (max 100 characters)');
    }
    
    // Check for invalid characters
    const invalidChars = /[<>\"'&]/;
    if (invalidChars.test(trimmedDept)) {
      errors.push('Department name contains invalid characters');
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      cleanDepartment: trimmedDept
    };
  }

  /**
   * Validate form submission data
   * @param {Object} formData - Form data to validate
   * @returns {Object}
   */
  static validateFormSubmission(formData) {
    const errors = [];
    
    if (!formData) {
      errors.push('Form data is required');
      return { isValid: false, errors };
    }
    
    if (!formData.namedValues) {
      errors.push('Form data must contain namedValues');
      return { isValid: false, errors };
    }

    // Validate required fields
    const requiredFields = [
      AppConfig.FORM_FIELDS.NAME,
      AppConfig.FORM_FIELDS.TODAY
    ];

    requiredFields.forEach(field => {
      const value = formData.namedValues[field]?.[0]?.trim();
      if (!value) {
        errors.push(`Field '${field}' is required`);
      }
    });

    // Validate timestamp
    const timestamp = formData.namedValues[AppConfig.FORM_FIELDS.TIMESTAMP]?.[0];
    if (!timestamp) {
      errors.push('Timestamp is required');
    } else {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        errors.push('Invalid timestamp format');
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * Sanitize text input
   * @param {string} text - Text to sanitize
   * @returns {string}
   */
  static sanitizeText(text) {
    if (!text || typeof text !== 'string') return '';
    
    return text.trim()
               .replace(/[<>\"'&]/g, '') // Remove potentially dangerous characters
               .replace(/\s+/g, ' '); // Normalize whitespace
  }

  /**
   * Validate numeric range
   * @param {*} value - Value to validate
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @returns {Object}
   */
  static validateNumericRange(value, min, max) {
    const errors = [];
    
    const numericValue = Number(value);
    
    if (isNaN(numericValue)) {
      errors.push('Value must be a valid number');
    } else {
      if (numericValue < min) {
        errors.push(`Value must be at least ${min}`);
      }
      
      if (numericValue > max) {
        errors.push(`Value cannot exceed ${max}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      value: numericValue
    };
  }
}
