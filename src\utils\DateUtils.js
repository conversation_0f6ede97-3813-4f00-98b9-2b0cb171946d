/**
 * Date Utilities
 * Following Single Responsibility Principle - only handles date operations
 */

class DateUtils {
  /**
   * Parse date from various formats
   * @param {*} dateInput - Date input (string, number, or Date)
   * @returns {Date|null}
   */
  static parseDate(dateInput) {
    if (!dateInput) return null;
    
    if (dateInput instanceof Date) {
      return isNaN(dateInput.getTime()) ? null : dateInput;
    }
    
    if (typeof dateInput === 'number') {
      return new Date(dateInput);
    }
    
    if (typeof dateInput === 'string') {
      // Try different date formats
      const formats = [
        // ISO format
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/,
        // DD/MM/YYYY format
        /^\d{1,2}\/\d{1,2}\/\d{4}/,
        // MM/DD/YYYY format
        /^\d{1,2}\/\d{1,2}\/\d{4}/,
        // YYYY-MM-DD format
        /^\d{4}-\d{2}-\d{2}/
      ];
      
      const date = new Date(dateInput);
      return isNaN(date.getTime()) ? null : date;
    }
    
    return null;
  }

  /**
   * Format date to string
   * @param {Date} date - Date to format
   * @param {string} format - Format string
   * @param {string} timezone - Timezone
   * @returns {string}
   */
  static formatDate(date, format = "dd/MM/yyyy", timezone = null) {
    if (!date || !(date instanceof Date)) return '';
    
    const tz = timezone || Session.getScriptTimeZone();
    return Utilities.formatDate(date, tz, format);
  }

  /**
   * Check if date is today
   * @param {Date} date - Date to check
   * @returns {boolean}
   */
  static isToday(date) {
    if (!date) return false;
    
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  }

  /**
   * Check if date is yesterday
   * @param {Date} date - Date to check
   * @returns {boolean}
   */
  static isYesterday(date) {
    if (!date) return false;
    
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    return date.getDate() === yesterday.getDate() &&
           date.getMonth() === yesterday.getMonth() &&
           date.getFullYear() === yesterday.getFullYear();
  }

  /**
   * Check if date is weekend
   * @param {Date} date - Date to check
   * @returns {boolean}
   */
  static isWeekend(date) {
    if (!date) return false;
    const day = date.getDay();
    return day === 0 || day === 6;
  }

  /**
   * Get days between two dates
   * @param {Date} date1 - First date
   * @param {Date} date2 - Second date
   * @returns {number}
   */
  static getDaysBetween(date1, date2) {
    if (!date1 || !date2) return 0;
    
    const timeDiff = Math.abs(date2.getTime() - date1.getTime());
    return Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  }

  /**
   * Get hours between two dates
   * @param {Date} date1 - First date
   * @param {Date} date2 - Second date
   * @returns {number}
   */
  static getHoursBetween(date1, date2) {
    if (!date1 || !date2) return 0;
    
    const timeDiff = Math.abs(date2.getTime() - date1.getTime());
    return timeDiff / (1000 * 60 * 60);
  }

  /**
   * Get start of day
   * @param {Date} date - Date
   * @returns {Date}
   */
  static getStartOfDay(date) {
    if (!date) return null;
    
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    return startOfDay;
  }

  /**
   * Get end of day
   * @param {Date} date - Date
   * @returns {Date}
   */
  static getEndOfDay(date) {
    if (!date) return null;
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    return endOfDay;
  }

  /**
   * Get start of week (Monday)
   * @param {Date} date - Date
   * @returns {Date}
   */
  static getStartOfWeek(date) {
    if (!date) return null;
    
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Monday start
    startOfWeek.setDate(diff);
    startOfWeek.setHours(0, 0, 0, 0);
    return startOfWeek;
  }

  /**
   * Get end of week (Sunday)
   * @param {Date} date - Date
   * @returns {Date}
   */
  static getEndOfWeek(date) {
    if (!date) return null;
    
    const startOfWeek = this.getStartOfWeek(date);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);
    return endOfWeek;
  }

  /**
   * Get start of month
   * @param {Date} date - Date
   * @returns {Date}
   */
  static getStartOfMonth(date) {
    if (!date) return null;
    
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    startOfMonth.setHours(0, 0, 0, 0);
    return startOfMonth;
  }

  /**
   * Get end of month
   * @param {Date} date - Date
   * @returns {Date}
   */
  static getEndOfMonth(date) {
    if (!date) return null;
    
    const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    endOfMonth.setHours(23, 59, 59, 999);
    return endOfMonth;
  }

  /**
   * Check if date is palindrome
   * @param {Date} date - Date to check
   * @returns {boolean}
   */
  static isPalindromeDate(date) {
    if (!date) return false;
    
    const dateStr = this.formatDate(date, "dd/MM/yyyy");
    const cleanDate = dateStr.replace(/\//g, "");
    return cleanDate === cleanDate.split("").reverse().join("");
  }

  /**
   * Get timezone offset in hours
   * @returns {number}
   */
  static getTimezoneOffset() {
    return new Date().getTimezoneOffset() / 60;
  }

  /**
   * Convert to timezone
   * @param {Date} date - Date to convert
   * @param {string} timezone - Target timezone
   * @returns {Date}
   */
  static convertToTimezone(date, timezone) {
    if (!date) return null;
    
    try {
      return new Date(date.toLocaleString("en-US", { timeZone: timezone }));
    } catch (error) {
      console.log(`❌ Error converting timezone: ${error.message}`);
      return date;
    }
  }

  /**
   * Get relative time string
   * @param {Date} date - Date
   * @returns {string}
   */
  static getRelativeTime(date) {
    if (!date) return 'Unknown';
    
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffDays > 0) {
      return `${diffDays} día${diffDays > 1 ? 's' : ''} atrás`;
    } else if (diffHours > 0) {
      return `${diffHours} hora${diffHours > 1 ? 's' : ''} atrás`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minuto${diffMinutes > 1 ? 's' : ''} atrás`;
    } else {
      return 'Ahora mismo';
    }
  }
}
