/**
 * Legendary Achievement Configuration
 * Following Single Responsibility Principle - only manages legendary/rare achievements
 */

class LegendaryAchievementConfig {
  static get LEGENDARY_ACHIEVEMENTS() {
    return [
      // === SUPER RARE/LEGENDARY ACHIEVEMENTS ===
      {
        id: "midnight_warrior",
        name: "Guerrero de Medianoche",
        emoji: "🌙",
        points: 100,
        description: "Completar daily exactamente a las 00:00:00",
        category: "Legendario",
        condition: (data) => {
          return data.timestamp.getHours() === 0 &&
                 data.timestamp.getMinutes() === 0 &&
                 data.timestamp.getSeconds() === 0;
        }
      },
      {
        id: "eclipse_master",
        name: "Maestro del Eclipse",
        emoji: "🌘",
        points: 150,
        description: "Completar daily durante un eclipse solar (fechas específicas)",
        category: "Legendario",
        condition: (data) => {
          const eclipseDates = AppConfig.ECLIPSE_DATES;
          const dateStr = data.timestamp.toISOString().split('T')[0];
          return eclipseDates.includes(dateStr);
        }
      },
      {
        id: "palindrome_prophet",
        name: "Profeta del Palíndromo",
        emoji: "🔮",
        points: 75,
        description: "Completar daily en una fecha palíndromo (ej: 12/02/2021)",
        category: "Legendario",
        condition: (data) => {
          const dateStr = Utilities.formatDate(data.timestamp, Session.getScriptTimeZone(), "dd/MM/yyyy");
          const cleanDate = dateStr.replace(/\//g, "");
          return cleanDate === cleanDate.split("").reverse().join("");
        }
      },
      {
        id: "pi_day_genius",
        name: "Genio del Día Pi",
        emoji: "🥧",
        points: 50,
        description: "Completar daily el 14 de marzo a las 15:92 (imposible, pero si alguien lo hace...)",
        category: "Legendario",
        condition: (data) => {
          return data.timestamp.getMonth() === 2 &&
                 data.timestamp.getDate() === 14 &&
                 data.timestamp.getHours() === 15 &&
                 data.timestamp.getMinutes() === 92; // Impossible, but epic
        }
      },
      {
        id: "fibonacci_master",
        name: "Maestro de Fibonacci",
        emoji: "🌀",
        points: 89,
        description: "Completar daily cuando tu total de dailies sea un número de Fibonacci ≥21",
        category: "Legendario",
        condition: (_, userStats) => {
          const fibNumbers = AppConfig.FIBONACCI_NUMBERS;
          return fibNumbers.includes(userStats.totalDailies);
        }
      },
      {
        id: "prime_perfectionist",
        name: "Perfeccionista Primo",
        emoji: "🔢",
        points: 67,
        description: "Completar daily cuando tu total de dailies sea un número primo ≥67",
        category: "Legendario",
        condition: (_, userStats) => {
          const isPrime = (n) => {
            if (n < 67) return false;
            for (let i = 2; i <= Math.sqrt(n); i++) {
              if (n % i === 0) return false;
            }
            return true;
          };
          return isPrime(userStats.totalDailies);
        }
      },
      {
        id: "binary_prophet",
        name: "Profeta Binario",
        emoji: "💾",
        points: 64,
        description: "Completar daily cuando tu total de dailies sea una potencia de 2 ≥64",
        category: "Legendario",
        condition: (_, userStats) => {
          const n = userStats.totalDailies;
          return n >= 64 && (n & (n - 1)) === 0;
        }
      },
      {
        id: "time_traveler",
        name: "Viajero del Tiempo",
        emoji: "⏰",
        points: 88,
        description: "Completar daily a las 12:34:56",
        category: "Legendario",
        condition: (data) => {
          return data.timestamp.getHours() === 12 &&
                 data.timestamp.getMinutes() === 34 &&
                 data.timestamp.getSeconds() === 56;
        }
      },
      {
        id: "golden_ratio",
        name: "Ratio Dorado",
        emoji: "🏺",
        points: 161,
        description: "Completar daily cuando tu racha actual dividida por tu racha máxima se aproxime al ratio dorado (1.618)",
        category: "Legendario",
        condition: (_, userStats) => {
          if (userStats.maxStreak === 0) return false;
          const ratio = userStats.currentStreak / userStats.maxStreak;
          return Math.abs(ratio - 1.618) < 0.1;
        }
      },
      {
        id: "century_marker",
        name: "Marcador del Siglo",
        emoji: "💯",
        points: 200,
        description: "Completar exactamente 100 dailies",
        category: "Legendario",
        condition: (_, userStats) => {
          return userStats.totalDailies === 100;
        }
      },
      {
        id: "perfect_storm",
        name: "Tormenta Perfecta",
        emoji: "⛈️",
        points: 300,
        description: "Completar daily en Lucky Day, usando palabra del día, con 7+ logros en un día",
        category: "Legendario",
        condition: (data, userStats) => {
          // This will need to be implemented with proper service injection
          return false; // Placeholder for complex condition
        }
      }
    ];
  }

  // === PURE LUCK/COINCIDENCE ACHIEVEMENTS ===
  static get COINCIDENCE_ACHIEVEMENTS() {
    return [
      {
        id: "serendipity_master",
        name: "Maestro de la Serendipia",
        emoji: "🍀",
        points: 77,
        description: "Usar las palabras 'suerte', 'casualidad' y 'destino' en la misma daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return text.includes('suerte') && text.includes('casualidad') && text.includes('destino');
        }
      },
      {
        id: "matrix_glitch",
        name: "Glitch en la Matrix",
        emoji: "🔴",
        points: 99,
        description: "Mencionar 'bug', 'error' y 'matrix' en la misma daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return text.includes('bug') && text.includes('error') && text.includes('matrix');
        }
      },
      {
        id: "inception_dream",
        name: "Sueño de Inception",
        emoji: "🌀",
        points: 88,
        description: "Usar 'sueño', 'realidad' y 'despertar' en la misma daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return text.includes('sueño') && text.includes('realidad') && text.includes('despertar');
        }
      },
      {
        id: "time_paradox",
        name: "Paradoja Temporal",
        emoji: "⏳",
        points: 121,
        description: "Mencionar 'ayer', 'hoy' y 'mañana' exactamente en sus secciones correspondientes",
        category: "Legendario",
        condition: (data) => {
          return data.ayer.toLowerCase().includes('ayer') &&
                 data.hoy.toLowerCase().includes('hoy') &&
                 data.hoy.toLowerCase().includes('mañana');
        }
      },
      {
        id: "meta_achievement",
        name: "Logro Meta",
        emoji: "🎯",
        points: 111,
        description: "Mencionar 'logro', 'achievement' o 'badge' en tu daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return text.includes('logro') || text.includes('achievement') || text.includes('badge');
        }
      },
      {
        id: "recursive_daily",
        name: "Daily Recursiva",
        emoji: "🔄",
        points: 101,
        description: "Mencionar 'daily' en tu propia daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return text.includes('daily');
        }
      },
      {
        id: "emoji_prophet",
        name: "Profeta de Emojis",
        emoji: "😎",
        points: 55,
        description: "Usar exactamente 5 emojis diferentes en tu daily",
        category: "Legendario",
        condition: (data) => {
          const text = data.ayer + " " + data.hoy + " " + data.imped;
          const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
          const emojis = text.match(emojiRegex) || [];
          const uniqueEmojis = [...new Set(emojis)];
          return uniqueEmojis.length === 5;
        }
      },
      {
        id: "number_mystic",
        name: "Místico de los Números",
        emoji: "🔢",
        points: 42,
        description: "Mencionar exactamente 3 números diferentes en tu daily",
        category: "Legendario",
        condition: (data) => {
          const text = data.ayer + " " + data.hoy + " " + data.imped;
          const numbers = text.match(/\b\d+\b/g) || [];
          const uniqueNumbers = [...new Set(numbers)];
          return uniqueNumbers.length === 3;
        }
      },
      {
        id: "color_spectrum",
        name: "Espectro de Colores",
        emoji: "🌈",
        points: 70,
        description: "Mencionar 4 colores diferentes en tu daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          const colors = AppConfig.COLORS;
          const foundColors = colors.filter(color => text.includes(color));
          return foundColors.length >= 4;
        }
      },
      {
        id: "weather_oracle",
        name: "Oráculo del Clima",
        emoji: "🌦️",
        points: 60,
        description: "Mencionar 3 fenómenos meteorológicos en tu daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          const weather = AppConfig.WEATHER_TERMS;
          const foundWeather = weather.filter(w => text.includes(w));
          return foundWeather.length >= 3;
        }
      },
      {
        id: "animal_whisperer",
        name: "Susurrador de Animales",
        emoji: "🦁",
        points: 65,
        description: "Mencionar 5 animales diferentes en tu daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          const animals = AppConfig.ANIMALS;
          const foundAnimals = animals.filter(animal => text.includes(animal));
          return foundAnimals.length >= 5;
        }
      },
      {
        id: "tech_stack_master",
        name: "Maestro del Tech Stack",
        emoji: "💻",
        points: 80,
        description: "Mencionar 4 tecnologías de programación en tu daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          const techs = AppConfig.TECHNOLOGIES;
          const foundTechs = techs.filter(tech => text.includes(tech));
          return foundTechs.length >= 4;
        }
      },
      {
        id: "food_critic",
        name: "Crítico Gastronómico",
        emoji: "🍕",
        points: 50,
        description: "Mencionar 6 comidas diferentes en tu daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          const foods = AppConfig.FOODS;
          const foundFoods = foods.filter(food => text.includes(food));
          return foundFoods.length >= 6;
        }
      },
      {
        id: "universe_philosopher",
        name: "Filósofo del Universo",
        emoji: "🌌",
        points: 137,
        description: "Usar palabras filosóficas profundas: 'existencia', 'consciencia', 'realidad', 'infinito'",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          const philosophical = AppConfig.PHILOSOPHICAL_TERMS;
          return philosophical.every(word => text.includes(word));
        }
      },
      {
        id: "word_length_perfectionist",
        name: "Perfeccionista de Longitud",
        emoji: "📏",
        points: 123,
        description: "Escribir exactamente 123 caracteres en total (ayer + hoy + impedimentos)",
        category: "Legendario",
        condition: (data) => {
          const totalLength = (data.ayer + data.hoy + data.imped).length;
          return totalLength === 123;
        }
      }
    ];
  }

  // === PURE LUCK/COINCIDENCE ACHIEVEMENTS ===
  static get COINCIDENCE_ACHIEVEMENTS() {
    return [
      {
        id: "serendipity_master",
        name: "Maestro de la Serendipia",
        emoji: "🍀",
        points: 77,
        description: "Usar las palabras 'suerte', 'casualidad' y 'destino' en la misma daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return text.includes('suerte') && text.includes('casualidad') && text.includes('destino');
        }
      },
      {
        id: "matrix_glitch",
        name: "Glitch en la Matrix",
        emoji: "🔴",
        points: 99,
        description: "Mencionar 'bug', 'error' y 'matrix' en la misma daily",
        category: "Legendario",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return text.includes('bug') && text.includes('error') && text.includes('matrix');
        }
      }
    ];
  }

  // Get all legendary achievements including coincidence ones
  static getAllLegendaryAchievements() {
    return [
      ...this.LEGENDARY_ACHIEVEMENTS,
      ...this.COINCIDENCE_ACHIEVEMENTS
    ];
  }
}
