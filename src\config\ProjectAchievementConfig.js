/**
 * Project-specific Achievement Configuration
 * Following Single Responsibility Principle - only manages project-related achievements
 */

class ProjectAchievementConfig {
  static get PROJECT_ACHIEVEMENTS() {
    return [
      {
        id: "project_warrior",
        name: "<PERSON> del Proyecto",
        emoji: "⚔️",
        points: 15,
        description: "Mencionar trabajo específico del proyecto en daily",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(proyecto|feature|desarrollo|implementar|implementé|código|programar|programé|build|crear|creé)\b/i.test(text);
        }
      },
      {
        id: "problem_solver",
        name: "Solucionador",
        emoji: "🔧",
        points: 20,
        description: "Reportar solución de problemas/bugs",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(solucion|arregl|fix|resolv|complet|termin|finaliz|acabé|acabar|reparé|reparar)\b/i.test(text);
        }
      },
      {
        id: "bug_hunter",
        name: "Cazador de Bugs",
        emoji: "🐛",
        points: 18,
        description: "Encontrar y reportar bugs o problemas",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(bug|error|problema|fallo|issue|defecto|encontré|detecté|identifiqué)\b/i.test(text);
        }
      },
      {
        id: "builder",
        name: "Constructor",
        emoji: "🏗️",
        points: 16,
        description: "Construir o desarrollar nuevas funcionalidades",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(construir|construí|desarrollar|desarrollé|funcionalidad|component|módulo|clase|función)\b/i.test(text);
        }
      },
      {
        id: "tester",
        name: "Probador Experto",
        emoji: "🧪",
        points: 14,
        description: "Realizar testing o pruebas del proyecto",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(test|testing|prueba|probar|probé|verificar|verificué|validar|validé)\b/i.test(text);
        }
      },
      {
        id: "documenter",
        name: "Documentador",
        emoji: "📝",
        points: 12,
        description: "Crear o actualizar documentación",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(documentar|documenté|documentación|readme|wiki|comentar|comenté|explicar|expliqué)\b/i.test(text);
        }
      },
      {
        id: "deployer",
        name: "Lanzador",
        emoji: "🚀",
        points: 25,
        description: "Realizar deploy o lanzamiento de funcionalidades",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(deploy|desplegar|desplegué|lanzar|lancé|publicar|publiqué|release|subir|subí)\b/i.test(text);
        }
      },
      {
        id: "reviewer",
        name: "Revisor de Código",
        emoji: "👀",
        points: 13,
        description: "Revisar código de compañeros",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(revisar|revisé|review|pull request|pr|código|merge|aprobar|aprobé)\b/i.test(text);
        }
      },
      {
        id: "optimizer",
        name: "Optimizador",
        emoji: "⚡",
        points: 17,
        description: "Optimizar rendimiento o mejorar código existente",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(optimizar|optimicé|mejorar|mejoré|refactor|refactorizar|performance|rendimiento)\b/i.test(text);
        }
      },
      {
        id: "planner",
        name: "Planificador",
        emoji: "📋",
        points: 11,
        description: "Planificar tareas o arquitectura del proyecto",
        category: "Proyecto",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          return /\b(planificar|planifiqué|diseñar|diseñé|arquitectura|estructura|organizar|organicé)\b/i.test(text);
        }
      },

      // === DEPARTMENT ACHIEVEMENTS ===
      {
        id: "department_ambassador",
        name: "Embajador del Departamento",
        emoji: "🤝",
        points: 25,
        description: "Ser el más activo de tu departamento",
        category: "Departamento",
        condition: (_, userStats) => {
          return userStats.departmentRank === 1;
        }
      },
      {
        id: "team_leader",
        name: "Líder de Equipo",
        emoji: "👥",
        points: 20,
        description: "Superar el promedio del departamento por 20 dailies",
        category: "Departamento",
        condition: (_, userStats) => {
          return userStats.totalDailies > (userStats.departmentAverage + 20);
        }
      }
    ];
  }
}
