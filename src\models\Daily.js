/**
 * Daily Domain Model
 * Following Single Responsibility Principle - represents daily report entity and its behavior
 */

class Daily {
  constructor(data = {}) {
    this.userName = data.userName || data.nombre || '';
    this.timestamp = data.timestamp || new Date();
    this.yesterday = data.yesterday || data.ayer || '';
    this.today = data.today || data.hoy || '';
    this.impediments = data.impediments || data.imped || '';
  }

  /**
   * Get full text content of the daily
   * @returns {string}
   */
  getFullText() {
    return `${this.yesterday} ${this.today} ${this.impediments}`.trim();
  }

  /**
   * Get character count of the daily
   * @returns {number}
   */
  getCharacterCount() {
    return this.getFullText().length;
  }

  /**
   * Check if daily is detailed (over 200 characters)
   * @returns {boolean}
   */
  isDetailed() {
    return this.getCharacterCount() > 200;
  }

  /**
   * Check if daily is very detailed (over 300 characters)
   * @returns {boolean}
   */
  isVeryDetailed() {
    return this.getCharacterCount() > 300;
  }

  /**
   * Check if daily contains emojis
   * @returns {boolean}
   */
  hasEmojis() {
    const text = this.getFullText();
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
    return emojiRegex.test(text);
  }

  /**
   * Count unique emojis in daily
   * @returns {number}
   */
  countUniqueEmojis() {
    const text = this.getFullText();
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
    const emojis = text.match(emojiRegex) || [];
    return [...new Set(emojis)].length;
  }

  /**
   * Count unique numbers mentioned in daily
   * @returns {number}
   */
  countUniqueNumbers() {
    const text = this.getFullText();
    const numbers = text.match(/\b\d+\b/g) || [];
    return [...new Set(numbers)].length;
  }

  /**
   * Check if daily was submitted early (before 9 AM)
   * @returns {boolean}
   */
  isEarlySubmission() {
    return this.timestamp.getHours() < 9;
  }

  /**
   * Check if daily was submitted late (after 10 PM)
   * @returns {boolean}
   */
  isLateSubmission() {
    return this.timestamp.getHours() >= 22;
  }

  /**
   * Check if daily was submitted on weekend
   * @returns {boolean}
   */
  isWeekendSubmission() {
    const day = this.timestamp.getDay();
    return day === 0 || day === 6;
  }

  /**
   * Check if daily was submitted on Friday after 6 PM
   * @returns {boolean}
   */
  isFridayEveningSubmission() {
    return this.timestamp.getDay() === 5 && this.timestamp.getHours() >= 18;
  }

  /**
   * Check if daily contains specific word
   * @param {string} word - Word to search for
   * @returns {boolean}
   */
  containsWord(word) {
    if (!word) return false;
    const regex = new RegExp(`\\b${word}\\b`, 'i');
    return regex.test(this.getFullText());
  }

  /**
   * Check if daily contains any of the specified terms
   * @param {Array} terms - Array of terms to search for
   * @returns {boolean}
   */
  containsAnyTerm(terms) {
    const text = this.getFullText().toLowerCase();
    return terms.some(term => text.includes(term.toLowerCase()));
  }

  /**
   * Check if daily contains pattern
   * @param {RegExp} pattern - Regular expression pattern
   * @returns {boolean}
   */
  matchesPattern(pattern) {
    return pattern.test(this.getFullText());
  }

  /**
   * Get formatted date string
   * @param {string} format - Date format (default: "dd/MM/yyyy")
   * @returns {string}
   */
  getFormattedDate(format = "dd/MM/yyyy") {
    return Utilities.formatDate(this.timestamp, Session.getScriptTimeZone(), format);
  }

  /**
   * Check if daily is on specific date
   * @param {string} dateString - Date string in dd/MM/yyyy format
   * @returns {boolean}
   */
  isOnDate(dateString) {
    return this.getFormattedDate() === dateString;
  }

  /**
   * Convert to legacy format for backward compatibility
   * @returns {Object}
   */
  toLegacyFormat() {
    return {
      nombre: this.userName,
      timestamp: this.timestamp,
      ayer: this.yesterday,
      hoy: this.today,
      imped: this.impediments
    };
  }

  /**
   * Create Daily from form submission data
   * @param {Object} formData - Form submission data
   * @returns {Daily}
   */
  static fromFormSubmission(formData) {
    return new Daily({
      userName: formData.namedValues[AppConfig.FORM_FIELDS.NAME]?.[0]?.trim() || '',
      timestamp: new Date(formData.namedValues[AppConfig.FORM_FIELDS.TIMESTAMP]?.[0] || new Date()),
      yesterday: formData.namedValues[AppConfig.FORM_FIELDS.YESTERDAY]?.[0]?.trim() || '',
      today: formData.namedValues[AppConfig.FORM_FIELDS.TODAY]?.[0]?.trim() || '',
      impediments: formData.namedValues[AppConfig.FORM_FIELDS.IMPEDIMENTS]?.[0]?.trim() || ''
    });
  }

  /**
   * Validate daily data
   * @returns {Object} Validation result
   */
  validate() {
    const errors = [];
    
    if (!this.userName) {
      errors.push('User name is required');
    }
    
    if (!this.today) {
      errors.push('Today section is required');
    }
    
    if (!this.timestamp || isNaN(this.timestamp.getTime())) {
      errors.push('Valid timestamp is required');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}
