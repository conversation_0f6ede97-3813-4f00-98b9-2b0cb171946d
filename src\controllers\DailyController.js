/**
 * Daily Controller
 * Following Single Responsibility Principle - orchestrates daily submission process
 * Following Open/Closed Principle - extensible without modification
 */

class DailyController {
  constructor(userService, pointsService, achievementService, shieldService, notificationService, dailyRepository) {
    this.userService = userService;
    this.pointsService = pointsService;
    this.achievementService = achievementService;
    this.shieldService = shieldService;
    this.notificationService = notificationService;
    this.dailyRepository = dailyRepository;
  }

  /**
   * Process form submission
   * @param {Object} formData - Form submission data
   * @returns {Object} Processing result
   */
  async processFormSubmission(formData) {
    try {
      // 1. Create Daily object from form data
      const daily = Daily.fromFormSubmission(formData);
      
      // 2. Validate daily data
      const validation = daily.validate();
      if (!validation.isValid) {
        console.log(`❌ Invalid daily data: ${validation.errors.join(', ')}`);
        return { success: false, errors: validation.errors };
      }

      // 3. Save daily to repository
      const dailySaved = this.dailyRepository.saveDaily(daily);
      if (!dailySaved) {
        console.log(`❌ Failed to save daily for ${daily.userName}`);
        return { success: false, errors: ['Failed to save daily'] };
      }

      // 4. Process user data and streak
      const userResult = this.userService.processDailySubmission(daily);
      const user = userResult.user;

      // 5. Calculate points
      const pointsResult = this.pointsService.calculateDailyPoints(daily, user);
      
      // 6. Add points to user
      this.userService.addPointsToUser(user.name, pointsResult.finalPoints);
      
      // 7. Evaluate and grant achievements
      const earnedAchievements = this.achievementService.evaluateAchievements(daily, user);
      if (earnedAchievements.length > 0) {
        this.achievementService.grantAchievements(user.name, earnedAchievements);
        
        // Add achievement points
        const achievementPoints = earnedAchievements.reduce((sum, ach) => sum + ach.points, 0);
        this.userService.addPointsToUser(user.name, achievementPoints);
      }

      // 8. Check shield eligibility
      const shieldsGranted = this.shieldService.checkAndGrantShields();

      // 9. Send notifications
      await this.sendNotifications(daily, user, pointsResult, earnedAchievements);

      // 10. Return success result
      return {
        success: true,
        user: user,
        pointsEarned: pointsResult.finalPoints,
        achievementsEarned: earnedAchievements,
        shieldsGranted: shieldsGranted,
        streakResult: userResult.streakResult,
        isNewUser: userResult.isNewUser
      };

    } catch (error) {
      console.log(`❌ Error processing form submission: ${error.message}`);
      return { success: false, errors: [error.message] };
    }
  }

  /**
   * Send all notifications for daily submission
   * @param {Daily} daily - Daily object
   * @param {User} user - User object
   * @param {Object} pointsResult - Points calculation result
   * @param {Array} earnedAchievements - Earned achievements
   */
  async sendNotifications(daily, user, pointsResult, earnedAchievements) {
    try {
      // Send daily report
      await this.notificationService.sendDailyReport(daily, user, pointsResult.finalPoints);
      
      // Send achievement notifications
      if (earnedAchievements.length > 0) {
        await this.notificationService.sendAchievementNotification(user.name, earnedAchievements);
        this.achievementService.markAsNotified(user.name, earnedAchievements.map(ach => ach.id));
      }
      
    } catch (error) {
      console.log(`❌ Error sending notifications: ${error.message}`);
    }
  }

  /**
   * Get daily statistics
   * @param {string} userName - User name (optional)
   * @returns {Object}
   */
  getDailyStatistics(userName = null) {
    if (userName) {
      return this.userService.getUserStatistics(userName);
    } else {
      return this.userService.getOverallStatistics();
    }
  }

  /**
   * Get leaderboard
   * @param {string} sortBy - Sort criteria
   * @param {number} limit - Number of users
   * @param {string} department - Department filter (optional)
   * @returns {Array}
   */
  getLeaderboard(sortBy = 'points', limit = 10, department = null) {
    if (department) {
      return this.userService.getDepartmentLeaderboard(department, sortBy, limit);
    } else {
      return this.userService.getLeaderboard(sortBy, limit);
    }
  }

  /**
   * Get user dashboard data
   * @param {string} userName - User name
   * @returns {Object}
   */
  getUserDashboard(userName) {
    const userStats = this.userService.getUserStatistics(userName);
    if (!userStats) return null;

    const userAchievements = this.achievementService.getUserAchievements(userName);
    const pendingNotifications = this.achievementService.getPendingNotifications(userName);
    const shieldStatus = this.shieldService.getUserShieldStatus(userName);
    const pointsSummary = this.pointsService.getUserPointsSummary(userName);

    return {
      user: userStats,
      achievements: userAchievements,
      pendingNotifications: pendingNotifications,
      shieldStatus: shieldStatus,
      pointsSummary: pointsSummary,
      recentDailies: this.dailyRepository.getUserDailies(userName).slice(-5) // Last 5 dailies
    };
  }

  /**
   * Get recent activity
   * @param {number} days - Number of days to look back
   * @returns {Object}
   */
  getRecentActivity(days = 7) {
    const recentDailies = this.dailyRepository.getRecentDailies(days);
    const userCounts = {};
    
    recentDailies.forEach(daily => {
      userCounts[daily.userName] = (userCounts[daily.userName] || 0) + 1;
    });

    const topActiveUsers = Object.entries(userCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([name, count]) => ({ name, dailies: count }));

    return {
      totalDailies: recentDailies.length,
      uniqueUsers: Object.keys(userCounts).length,
      topActiveUsers: topActiveUsers,
      dailiesPerDay: Math.round((recentDailies.length / days) * 100) / 100
    };
  }

  /**
   * Process pending notifications for all users
   * @returns {Object}
   */
  async processPendingNotifications() {
    const users = this.userService.userRepository.getAllUsers();
    let notificationsSent = 0;
    let errors = 0;

    for (const user of users) {
      try {
        const pendingAchievements = this.achievementService.getPendingNotifications(user.name);
        
        if (pendingAchievements.length > 0) {
          const success = await this.notificationService.sendAchievementNotification(
            user.name, 
            pendingAchievements
          );
          
          if (success) {
            this.achievementService.markAsNotified(
              user.name, 
              pendingAchievements.map(ach => ach.achievementId)
            );
            notificationsSent++;
          } else {
            errors++;
          }
        }
      } catch (error) {
        console.log(`❌ Error processing notifications for ${user.name}: ${error.message}`);
        errors++;
      }
    }

    return {
      notificationsSent: notificationsSent,
      errors: errors,
      totalUsers: users.length
    };
  }

  /**
   * Validate daily submission
   * @param {Object} formData - Form data
   * @returns {Object} Validation result
   */
  validateDailySubmission(formData) {
    const errors = [];
    
    if (!formData.namedValues) {
      errors.push('Invalid form data structure');
      return { isValid: false, errors };
    }

    const userName = formData.namedValues[AppConfig.FORM_FIELDS.NAME]?.[0]?.trim();
    const today = formData.namedValues[AppConfig.FORM_FIELDS.TODAY]?.[0]?.trim();
    
    if (!userName) {
      errors.push('User name is required');
    }
    
    if (!today) {
      errors.push('Today section is required');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}
