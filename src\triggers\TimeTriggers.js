/**
 * Google Apps Script Time Triggers
 * Following Single Responsibility Principle - only handles scheduled tasks
 */

/**
 * Daily shield protection check
 * Runs every day to check shield protection and grant new shields
 */
function dailyShieldCheck() {
  try {
    console.log('🛡️ Starting daily shield protection check');
    
    const container = getContainer();
    const adminController = container.getAdminController();
    
    const result = adminController.processShieldProtection();
    
    if (result.success) {
      console.log(`✅ Shield check completed:`);
      console.log(`  - Protected users: ${result.protectedUsers}`);
      console.log(`  - New shields granted: ${result.newShields}`);
      
      if (result.protectedUsers > 0 || result.newShields > 0) {
        const discordService = container.getDiscordService();
        const message = `🛡️ **Daily Shield Check**\n` +
                       `Protected users: ${result.protectedUsers}\n` +
                       `New shields granted: ${result.newShields}`;
        discordService.sendMessage(message);
      }
    } else {
      console.log(`❌ Shield check failed: ${result.error}`);
    }
    
    return result;
  } catch (error) {
    console.log(`❌ Critical error in dailyShieldCheck: ${error.message}`);
    
    // Send error notification
    try {
      const container = getContainer();
      const discordService = container.getDiscordService();
      discordService.sendMessage(`🚨 **Error en shield check diario**\n\`\`\`${error.message}\`\`\``);
    } catch (notificationError) {
      console.log(`❌ Failed to send shield check error notification: ${notificationError.message}`);
    }
    
    return { success: false, error: error.message };
  }
}

/**
 * Process pending achievement notifications
 * Runs periodically to send pending achievement notifications
 */
function processPendingNotifications() {
  try {
    console.log('🏆 Processing pending achievement notifications');
    
    const container = getContainer();
    const dailyController = container.getDailyController();
    
    const result = dailyController.processPendingNotifications();
    
    console.log(`✅ Notification processing completed:`);
    console.log(`  - Notifications sent: ${result.notificationsSent}`);
    console.log(`  - Errors: ${result.errors}`);
    console.log(`  - Total users checked: ${result.totalUsers}`);
    
    return result;
  } catch (error) {
    console.log(`❌ Critical error in processPendingNotifications: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Weekly statistics report
 * Sends a weekly summary to Discord
 */
function weeklyStatsReport() {
  try {
    console.log('📊 Generating weekly statistics report');
    
    const container = getContainer();
    const adminController = container.getAdminController();
    const dailyController = container.getDailyController();
    const discordService = container.getDiscordService();
    
    // Get system statistics
    const systemStats = adminController.getSystemStatistics();
    const recentActivity = dailyController.getRecentActivity(7);
    const topUsers = dailyController.getLeaderboard('points', 5);
    
    // Create report message
    const message = createWeeklyReportMessage(systemStats, recentActivity, topUsers);
    
    // Send to Discord
    discordService.sendMessage(message);
    
    console.log('✅ Weekly report sent successfully');
    return { success: true };
  } catch (error) {
    console.log(`❌ Error generating weekly report: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Create weekly report message
 * @param {Object} systemStats - System statistics
 * @param {Object} recentActivity - Recent activity data
 * @param {Array} topUsers - Top users leaderboard
 * @returns {string}
 */
function createWeeklyReportMessage(systemStats, recentActivity, topUsers) {
  const message = `📊 **Reporte Semanal - Daily Gamification**\n\n` +
    `**📈 Estadísticas del Sistema:**\n` +
    `• Total usuarios: ${systemStats.users.totalUsers}\n` +
    `• Total dailies: ${systemStats.users.totalDailies}\n` +
    `• Puntos promedio: ${systemStats.users.averagePoints}\n` +
    `• Racha más larga: ${systemStats.users.longestStreak} días\n\n` +
    
    `**🔥 Actividad de la Semana:**\n` +
    `• Dailies enviados: ${recentActivity.totalDailies}\n` +
    `• Usuarios activos: ${recentActivity.uniqueUsers}\n` +
    `• Promedio diario: ${recentActivity.dailiesPerDay}\n\n` +
    
    `**🏆 Top 5 Usuarios (Puntos):**\n` +
    topUsers.map((user, index) => 
      `${index + 1}. ${user.name} - ${user.value} pts ${user.badge}`
    ).join('\n') + '\n\n' +
    
    `**🛡️ Sistema de Escudos:**\n` +
    `• Usuarios con escudo: ${systemStats.shields.usersWithShields}\n` +
    `• Escudos utilizados: ${systemStats.shields.totalShieldsUsed}\n\n` +
    
    `**🎯 Logros:**\n` +
    `• Total logros únicos: ${systemStats.achievements.totalAchievements}\n` +
    `• Logros más raros: ${systemStats.achievements.rarestAchievements.map(a => a.id).join(', ')}\n\n` +
    
    `*Reporte generado automáticamente - ${new Date().toLocaleDateString()}*`;
  
  return message;
}

/**
 * Setup time-based triggers
 * Creates all necessary scheduled triggers
 */
function setupTimeTriggers() {
  try {
    // Remove existing time triggers
    removeTimeTriggers();
    
    // Daily shield check at 8:00 AM
    ScriptApp.newTrigger('dailyShieldCheck')
             .timeBased()
             .everyDays(1)
             .atHour(8)
             .create();
    
    // Process pending notifications every 30 minutes
    ScriptApp.newTrigger('processPendingNotifications')
             .timeBased()
             .everyMinutes(30)
             .create();
    
    // Weekly stats report on Mondays at 9:00 AM
    ScriptApp.newTrigger('weeklyStatsReport')
             .timeBased()
             .onWeekDay(ScriptApp.WeekDay.MONDAY)
             .atHour(9)
             .create();
    
    console.log('✅ Time triggers setup successfully');
    return true;
  } catch (error) {
    console.log(`❌ Error setting up time triggers: ${error.message}`);
    return false;
  }
}

/**
 * Remove all time-based triggers
 */
function removeTimeTriggers() {
  try {
    const triggers = ScriptApp.getProjectTriggers();
    const timeTriggers = triggers.filter(trigger => 
      trigger.getEventType() === ScriptApp.EventType.CLOCK
    );
    
    timeTriggers.forEach(trigger => {
      ScriptApp.deleteTrigger(trigger);
    });
    
    console.log(`✅ Removed ${timeTriggers.length} time trigger(s)`);
    return true;
  } catch (error) {
    console.log(`❌ Error removing time triggers: ${error.message}`);
    return false;
  }
}

/**
 * Validate time trigger setup
 */
function validateTimeTriggers() {
  try {
    const triggers = ScriptApp.getProjectTriggers();
    const timeTriggers = triggers.filter(trigger => 
      trigger.getEventType() === ScriptApp.EventType.CLOCK
    );
    
    console.log(`📋 Found ${timeTriggers.length} time trigger(s)`);
    
    timeTriggers.forEach((trigger, index) => {
      console.log(`Trigger ${index + 1}:`);
      console.log(`  - Handler: ${trigger.getHandlerFunction()}`);
      console.log(`  - Source: ${trigger.getTriggerSource()}`);
    });
    
    const expectedTriggers = ['dailyShieldCheck', 'processPendingNotifications', 'weeklyStatsReport'];
    const actualTriggers = timeTriggers.map(t => t.getHandlerFunction());
    
    const missingTriggers = expectedTriggers.filter(expected => 
      !actualTriggers.includes(expected)
    );
    
    if (missingTriggers.length > 0) {
      console.log(`⚠️ Missing triggers: ${missingTriggers.join(', ')}`);
      return false;
    }
    
    console.log('✅ All time triggers are properly configured');
    return true;
  } catch (error) {
    console.log(`❌ Error validating time triggers: ${error.message}`);
    return false;
  }
}

/**
 * Manual trigger for testing shield check
 */
function testShieldCheck() {
  console.log('🧪 Manual shield check test');
  return dailyShieldCheck();
}

/**
 * Manual trigger for testing notification processing
 */
function testNotificationProcessing() {
  console.log('🧪 Manual notification processing test');
  return processPendingNotifications();
}

/**
 * Manual trigger for testing weekly report
 */
function testWeeklyReport() {
  console.log('🧪 Manual weekly report test');
  return weeklyStatsReport();
}
