/**
 * Shield Protection Service
 * Following Single Responsibility Principle - only handles shield system logic
 */

class ShieldService {
  constructor(userRepository, notificationService) {
    this.userRepository = userRepository;
    this.notificationService = notificationService;
  }

  /**
   * Check and grant shields to eligible users
   * @returns {Array} Array of users who received shields
   */
  checkAndGrantShields() {
    const users = this.userRepository.getAllUsers();
    const shieldsGranted = [];

    for (const user of users) {
      if (user.isEligibleForFirstShield()) {
        this.grantFirstShield(user);
        shieldsGranted.push({ user: user, type: 'first' });
      } else if (user.isEligibleForShieldRegeneration()) {
        this.regenerateShield(user);
        shieldsGranted.push({ user: user, type: 'regenerated' });
      }
    }

    return shieldsGranted;
  }

  /**
   * Grant first shield to user
   * @param {User} user - User object
   * @returns {boolean}
   */
  grantFirstShield(user) {
    try {
      user.shieldAvailable = true;
      user.shieldLock = false;
      
      const success = this.userRepository.saveUser(user);
      
      if (success) {
        this.notificationService.sendShieldNotification(user.name, 'granted', {
          streak: user.currentStreak
        });
        
        console.log(`🛡️ First shield granted to ${user.name} (streak: ${user.currentStreak})`);
      }
      
      return success;
    } catch (error) {
      console.log(`❌ Error granting first shield to ${user.name}: ${error.message}`);
      return false;
    }
  }

  /**
   * Regenerate shield for user
   * @param {User} user - User object
   * @returns {boolean}
   */
  regenerateShield(user) {
    try {
      user.shieldAvailable = true;
      user.shieldLock = false;
      
      const success = this.userRepository.saveUser(user);
      
      if (success) {
        this.notificationService.sendShieldNotification(user.name, 'restored', {
          streak: user.currentStreak
        });
        
        console.log(`✨ Shield regenerated for ${user.name} (streak: ${user.currentStreak})`);
      }
      
      return success;
    } catch (error) {
      console.log(`❌ Error regenerating shield for ${user.name}: ${error.message}`);
      return false;
    }
  }

  /**
   * Consume shield to protect streak
   * @param {User} user - User object
   * @returns {boolean}
   */
  consumeShield(user) {
    if (!user.shieldAvailable) {
      console.log(`❌ ${user.name} doesn't have a shield available`);
      return false;
    }

    try {
      user.shieldAvailable = false;
      user.shieldLock = true;
      user.shieldLastUsed = new Date();
      
      const success = this.userRepository.saveUser(user);
      
      if (success) {
        this.notificationService.sendShieldNotification(user.name, 'consumed', {
          streak: user.currentStreak
        });
        
        console.log(`💥 Shield consumed for ${user.name} to protect streak of ${user.currentStreak}`);
      }
      
      return success;
    } catch (error) {
      console.log(`❌ Error consuming shield for ${user.name}: ${error.message}`);
      return false;
    }
  }

  /**
   * Check users who need shield protection
   * @returns {Array} Users who need shield check
   */
  checkUsersNeedingProtection() {
    const users = this.userRepository.getAllUsers();
    const now = new Date();
    const usersNeedingProtection = [];

    for (const user of users) {
      if (this.userNeedsShieldProtection(user, now)) {
        usersNeedingProtection.push(user);
      }
    }

    return usersNeedingProtection;
  }

  /**
   * Check if user needs shield protection
   * @param {User} user - User object
   * @param {Date} currentTime - Current time
   * @returns {boolean}
   */
  userNeedsShieldProtection(user, currentTime) {
    if (!user.lastDate || user.currentStreak === 0 || !user.shieldAvailable) {
      return false;
    }

    const lastDaily = user.lastDate instanceof Date ? user.lastDate : new Date(user.lastDate);
    const hoursSinceLastDaily = (currentTime - lastDaily) / (1000 * 60 * 60);
    
    // If it's past the shield check hour and user hasn't submitted today
    return hoursSinceLastDaily > AppConfig.TIME_CONFIG.SHIELD_CHECK_HOUR;
  }

  /**
   * Process shield protection for users with broken streaks
   * @returns {Array} Users who had shields consumed
   */
  processShieldProtection() {
    const users = this.userRepository.getAllUsers();
    const now = new Date();
    const protectedUsers = [];

    for (const user of users) {
      if (this.shouldConsumeShield(user, now)) {
        if (this.consumeShield(user)) {
          protectedUsers.push(user);
        }
      }
    }

    return protectedUsers;
  }

  /**
   * Check if shield should be consumed for user
   * @param {User} user - User object
   * @param {Date} currentTime - Current time
   * @returns {boolean}
   */
  shouldConsumeShield(user, currentTime) {
    if (!user.shieldAvailable || user.currentStreak === 0 || !user.lastDate) {
      return false;
    }

    const lastDaily = user.lastDate instanceof Date ? user.lastDate : new Date(user.lastDate);
    const daysSinceLastDaily = Math.floor((currentTime - lastDaily) / (1000 * 60 * 60 * 24));
    
    // Consume shield if more than 1 day has passed without a daily
    return daysSinceLastDaily > 1;
  }

  /**
   * Get shield statistics
   * @returns {Object}
   */
  getShieldStatistics() {
    const users = this.userRepository.getAllUsers();
    
    const stats = {
      totalUsers: users.length,
      usersWithShields: 0,
      usersWithUsedShields: 0,
      usersWithLockedShields: 0,
      totalShieldsUsed: 0
    };

    users.forEach(user => {
      if (user.shieldAvailable) {
        stats.usersWithShields++;
      }
      
      if (user.hasUsedShieldBefore()) {
        stats.usersWithUsedShields++;
        stats.totalShieldsUsed++;
      }
      
      if (user.shieldLock) {
        stats.usersWithLockedShields++;
      }
    });

    return stats;
  }

  /**
   * Get users with shields
   * @returns {Array<User>}
   */
  getUsersWithShields() {
    const users = this.userRepository.getAllUsers();
    return users.filter(user => user.shieldAvailable);
  }

  /**
   * Get users who have used shields
   * @returns {Array<User>}
   */
  getUsersWhoUsedShields() {
    const users = this.userRepository.getAllUsers();
    return users.filter(user => user.hasUsedShieldBefore());
  }

  /**
   * Reset shield for user (admin function)
   * @param {string} userName - User name
   * @returns {boolean}
   */
  resetUserShield(userName) {
    const user = this.userRepository.getByName(userName);
    if (!user) return false;
    
    user.shieldAvailable = false;
    user.shieldLock = false;
    user.shieldLastUsed = null;
    
    return this.userRepository.saveUser(user);
  }

  /**
   * Force grant shield to user (admin function)
   * @param {string} userName - User name
   * @returns {boolean}
   */
  forceGrantShield(userName) {
    const user = this.userRepository.getByName(userName);
    if (!user) return false;
    
    user.shieldAvailable = true;
    user.shieldLock = false;
    
    const success = this.userRepository.saveUser(user);
    
    if (success) {
      this.notificationService.sendShieldNotification(user.name, 'granted', {
        streak: user.currentStreak
      });
    }
    
    return success;
  }

  /**
   * Get shield status for user
   * @param {string} userName - User name
   * @returns {Object|null}
   */
  getUserShieldStatus(userName) {
    const user = this.userRepository.getByName(userName);
    if (!user) return null;
    
    return {
      available: user.shieldAvailable,
      locked: user.shieldLock,
      lastUsed: user.shieldLastUsed,
      eligibleForFirst: user.isEligibleForFirstShield(),
      eligibleForRegeneration: user.isEligibleForShieldRegeneration(),
      hasUsedBefore: user.hasUsedShieldBefore()
    };
  }
}
