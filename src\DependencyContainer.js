/**
 * Dependency Injection Container
 * Following Dependency Inversion Principle - manages all dependencies
 * Following Single Responsibility Principle - only handles dependency creation and injection
 */

class DependencyContainer {
  constructor() {
    this._instances = new Map();
    this._singletons = new Map();
  }

  /**
   * Register a singleton service
   * @param {string} name - Service name
   * @param {Function} factory - Factory function
   */
  registerSingleton(name, factory) {
    this._singletons.set(name, factory);
  }

  /**
   * Register a transient service
   * @param {string} name - Service name
   * @param {Function} factory - Factory function
   */
  registerTransient(name, factory) {
    this._instances.set(name, factory);
  }

  /**
   * Resolve service by name
   * @param {string} name - Service name
   * @returns {*} Service instance
   */
  resolve(name) {
    // Check singletons first
    if (this._singletons.has(name)) {
      const cacheKey = `singleton_${name}`;
      if (!this._instances.has(cacheKey)) {
        const factory = this._singletons.get(name);
        const instance = factory(this);
        this._instances.set(cacheKey, instance);
      }
      return this._instances.get(cacheKey);
    }

    // Check transients
    if (this._instances.has(name)) {
      const factory = this._instances.get(name);
      return factory(this);
    }

    throw new Error(`Service '${name}' not registered`);
  }

  /**
   * Initialize all services and dependencies
   */
  initialize() {
    // Register repositories (singletons)
    this.registerSingleton('userRepository', () => new UserRepository());
    this.registerSingleton('achievementRepository', () => new AchievementRepository());
    this.registerSingleton('gameConfigRepository', () => new GameConfigRepository());
    this.registerSingleton('dailyRepository', () => new DailyRepository());

    // Register services (singletons)
    this.registerSingleton('discordService', () => new DiscordService());
    
    this.registerSingleton('userService', (container) => 
      new UserService(
        container.resolve('userRepository'),
        container.resolve('dailyRepository')
      )
    );

    this.registerSingleton('pointsService', (container) => 
      new PointsService(
        container.resolve('gameConfigRepository'),
        container.resolve('dailyRepository')
      )
    );

    this.registerSingleton('achievementService', (container) => 
      new AchievementService(
        container.resolve('achievementRepository'),
        container.resolve('userRepository')
      )
    );

    this.registerSingleton('shieldService', (container) => 
      new ShieldService(
        container.resolve('userRepository'),
        container.resolve('discordService')
      )
    );

    // Register controllers (transients)
    this.registerTransient('dailyController', (container) => 
      new DailyController(
        container.resolve('userService'),
        container.resolve('pointsService'),
        container.resolve('achievementService'),
        container.resolve('shieldService'),
        container.resolve('discordService'),
        container.resolve('dailyRepository')
      )
    );

    this.registerTransient('adminController', (container) => 
      new AdminController(
        container.resolve('userService'),
        container.resolve('achievementService'),
        container.resolve('shieldService'),
        container.resolve('pointsService'),
        container.resolve('gameConfigRepository')
      )
    );
  }

  /**
   * Get daily controller instance
   * @returns {DailyController}
   */
  getDailyController() {
    return this.resolve('dailyController');
  }

  /**
   * Get admin controller instance
   * @returns {AdminController}
   */
  getAdminController() {
    return this.resolve('adminController');
  }

  /**
   * Get user service instance
   * @returns {UserService}
   */
  getUserService() {
    return this.resolve('userService');
  }

  /**
   * Get achievement service instance
   * @returns {AchievementService}
   */
  getAchievementService() {
    return this.resolve('achievementService');
  }

  /**
   * Get shield service instance
   * @returns {ShieldService}
   */
  getShieldService() {
    return this.resolve('shieldService');
  }

  /**
   * Get points service instance
   * @returns {PointsService}
   */
  getPointsService() {
    return this.resolve('pointsService');
  }

  /**
   * Get Discord service instance
   * @returns {DiscordService}
   */
  getDiscordService() {
    return this.resolve('discordService');
  }

  /**
   * Clear all singleton instances (for testing)
   */
  clearSingletons() {
    const singletonKeys = Array.from(this._instances.keys()).filter(key => key.startsWith('singleton_'));
    singletonKeys.forEach(key => this._instances.delete(key));
  }

  /**
   * Check if service is registered
   * @param {string} name - Service name
   * @returns {boolean}
   */
  isRegistered(name) {
    return this._singletons.has(name) || this._instances.has(name);
  }

  /**
   * Get all registered service names
   * @returns {Array<string>}
   */
  getRegisteredServices() {
    const singletons = Array.from(this._singletons.keys());
    const transients = Array.from(this._instances.keys()).filter(key => !key.startsWith('singleton_'));
    return [...singletons, ...transients];
  }
}

// Global container instance
let globalContainer = null;

/**
 * Get global dependency container
 * @returns {DependencyContainer}
 */
function getContainer() {
  if (!globalContainer) {
    globalContainer = new DependencyContainer();
    globalContainer.initialize();
  }
  return globalContainer;
}

/**
 * Reset global container (for testing)
 */
function resetContainer() {
  globalContainer = null;
}
