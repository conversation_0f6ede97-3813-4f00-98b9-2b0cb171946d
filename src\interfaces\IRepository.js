/**
 * Repository Interface
 * Following Interface Segregation Principle - defines minimal contract for data access
 */

class IRepository {
  /**
   * Get all records
   * @returns {Array} Array of records
   */
  getAll() {
    throw new Error("Method 'getAll' must be implemented");
  }

  /**
   * Get record by ID
   * @param {string} id - Record identifier
   * @returns {Object|null} Record or null if not found
   */
  getById(id) {
    throw new Error("Method 'getById' must be implemented");
  }

  /**
   * Create new record
   * @param {Object} data - Record data
   * @returns {boolean} Success status
   */
  create(data) {
    throw new Error("Method 'create' must be implemented");
  }

  /**
   * Update existing record
   * @param {string} id - Record identifier
   * @param {Object} data - Updated data
   * @returns {boolean} Success status
   */
  update(id, data) {
    throw new Error("Method 'update' must be implemented");
  }

  /**
   * Delete record
   * @param {string} id - Record identifier
   * @returns {boolean} Success status
   */
  delete(id) {
    throw new Error("Method 'delete' must be implemented");
  }
}
