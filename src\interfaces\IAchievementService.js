/**
 * Achievement Service Interface
 * Following Interface Segregation Principle - defines contract for achievement management
 */

class IAchievementService {
  /**
   * Evaluate achievements for a daily entry
   * @param {Object} dailyData - Daily data
   * @param {Object} userStats - User statistics
   * @returns {Array} Array of earned achievements
   */
  evaluateAchievements(dailyData, userStats) {
    throw new Error("Method 'evaluateAchievements' must be implemented");
  }

  /**
   * Grant achievement to user
   * @param {string} userName - User name
   * @param {string} achievementId - Achievement ID
   * @returns {boolean} Success status
   */
  grantAchievement(userName, achievementId) {
    throw new Error("Method 'grantAchievement' must be implemented");
  }

  /**
   * Check if user has specific achievement
   * @param {string} userName - User name
   * @param {string} achievementId - Achievement ID
   * @returns {boolean} Whether user has achievement
   */
  hasUserAchievement(userName, achievementId) {
    throw new Error("Method 'hasUserAchievement' must be implemented");
  }

  /**
   * Get all achievements for a user
   * @param {string} userName - User name
   * @returns {Array} Array of user achievements
   */
  getUserAchievements(userName) {
    throw new Error("Method 'getUserAchievements' must be implemented");
  }

  /**
   * Get pending notifications for a user
   * @param {string} userName - User name
   * @returns {Array} Array of pending achievement notifications
   */
  getPendingNotifications(userName) {
    throw new Error("Method 'getPendingNotifications' must be implemented");
  }

  /**
   * Mark achievements as notified
   * @param {string} userName - User name
   * @param {Array} achievementIds - Array of achievement IDs
   * @returns {boolean} Success status
   */
  markAsNotified(userName, achievementIds) {
    throw new Error("Method 'markAsNotified' must be implemented");
  }
}
