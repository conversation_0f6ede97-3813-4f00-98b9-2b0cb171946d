/**
 * Daily Repository
 * Following Single Responsibility Principle - handles daily report data persistence
 */

class DailyRepository extends BaseRepository {
  constructor() {
    const headers = ["Marca temporal", "IDENTIFICATE🤬", "¿Qué hiciste ayer?", "¿Qué vas a hacer hoy?", "¿Tienes algún impedimento?"];
    super(AppConfig.SHEET_NAMES.SOURCE, headers);
  }

  /**
   * Get all dailies for a user
   * @param {string} userName - User name
   * @returns {Array<Daily>}
   */
  getUserDailies(userName) {
    const rows = this.getWhere(row => row[1] === userName);
    return rows.map(row => this.rowToDaily(row));
  }

  /**
   * Get recent dailies (last N days)
   * @param {number} days - Number of days to look back
   * @returns {Array<Daily>}
   */
  getRecentDailies(days = 7) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    const rows = this.getWhere(row => {
      const timestamp = new Date(row[0]);
      return timestamp >= cutoffDate;
    });
    
    return rows.map(row => this.rowToDaily(row));
  }

  /**
   * Get dailies for specific date
   * @param {Date} date - Target date
   * @returns {Array<Daily>}
   */
  getDailiesForDate(date) {
    const targetDateStr = Utilities.formatDate(date, Session.getScriptTimeZone(), "dd/MM/yyyy");
    
    const rows = this.getWhere(row => {
      const timestamp = new Date(row[0]);
      const rowDateStr = Utilities.formatDate(timestamp, Session.getScriptTimeZone(), "dd/MM/yyyy");
      return rowDateStr === targetDateStr;
    });
    
    return rows.map(row => this.rowToDaily(row));
  }

  /**
   * Get last daily for user
   * @param {string} userName - User name
   * @returns {Daily|null}
   */
  getLastDailyForUser(userName) {
    const userDailies = this.getUserDailies(userName);
    
    if (userDailies.length === 0) return null;
    
    // Sort by timestamp descending and return first
    userDailies.sort((a, b) => b.timestamp - a.timestamp);
    return userDailies[0];
  }

  /**
   * Get dailies count for user
   * @param {string} userName - User name
   * @returns {number}
   */
  getUserDailiesCount(userName) {
    return this.getWhere(row => row[1] === userName).length;
  }

  /**
   * Get dailies for date range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Array<Daily>}
   */
  getDailiesInRange(startDate, endDate) {
    const rows = this.getWhere(row => {
      const timestamp = new Date(row[0]);
      return timestamp >= startDate && timestamp <= endDate;
    });
    
    return rows.map(row => this.rowToDaily(row));
  }

  /**
   * Get longest daily for current month
   * @returns {Object|null}
   */
  getLongestDailyThisMonth() {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    const monthlyDailies = this.getDailiesInRange(startOfMonth, endOfMonth);
    
    if (monthlyDailies.length === 0) return null;
    
    let longest = monthlyDailies[0];
    let maxLength = longest.getCharacterCount();
    
    monthlyDailies.forEach(daily => {
      const length = daily.getCharacterCount();
      if (length > maxLength) {
        maxLength = length;
        longest = daily;
      }
    });
    
    return {
      daily: longest,
      length: maxLength
    };
  }

  /**
   * Get user statistics
   * @param {string} userName - User name
   * @returns {Object}
   */
  getUserStats(userName) {
    const userDailies = this.getUserDailies(userName);
    
    if (userDailies.length === 0) {
      return {
        totalDailies: 0,
        averageLength: 0,
        longestDaily: 0,
        daysOfWeekCompleted: 0,
        luckyDaysHit: 0,
        wordOfDayUsed: 0,
        sameHourStreak: 0,
        perfectWeeks: 0
      };
    }

    // Sort by date
    userDailies.sort((a, b) => a.timestamp - b.timestamp);
    
    const daysOfWeek = new Set();
    let totalLength = 0;
    let longestDaily = 0;
    
    userDailies.forEach(daily => {
      daysOfWeek.add(daily.timestamp.getDay());
      const length = daily.getCharacterCount();
      totalLength += length;
      longestDaily = Math.max(longestDaily, length);
    });

    return {
      totalDailies: userDailies.length,
      averageLength: Math.round(totalLength / userDailies.length),
      longestDaily: longestDaily,
      daysOfWeekCompleted: daysOfWeek.size,
      luckyDaysHit: 0, // Will be calculated by service
      wordOfDayUsed: 0, // Will be calculated by service
      sameHourStreak: 0, // Will be calculated by service
      perfectWeeks: 0 // Will be calculated by service
    };
  }

  /**
   * Convert sheet row to Daily object
   * @param {Array} row - Sheet row
   * @returns {Daily}
   */
  rowToDaily(row) {
    return new Daily({
      timestamp: new Date(row[0]),
      userName: row[1],
      yesterday: row[2] || '',
      today: row[3] || '',
      impediments: row[4] || ''
    });
  }

  /**
   * Save daily to sheet
   * @param {Daily} daily - Daily object
   * @returns {boolean}
   */
  saveDaily(daily) {
    const rowData = [
      daily.timestamp,
      daily.userName,
      daily.yesterday,
      daily.today,
      daily.impediments
    ];
    
    return this.create(rowData);
  }

  // Implementation of IRepository interface methods
  getById(id) {
    return this.getLastDailyForUser(id);
  }

  update(id, data) {
    // Dailies are typically not updated, only created
    return false;
  }

  delete(id) {
    // For safety, we don't allow deleting dailies
    return false;
  }
}
