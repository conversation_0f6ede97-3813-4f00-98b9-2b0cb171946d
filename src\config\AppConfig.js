/**
 * Application Configuration
 * Centralizes all configuration constants following Single Responsibility Principle
 */

class AppConfig {
  // Discord Configuration
  static get DISCORD_WEBHOOK_URL() {
    return "https://discord.com/api/webhooks/1387858289873916025/i4FRnGq-SgdzNL7e7vNtbx4ouJZfH53ls_-ZWxMsGKRnYrN7LHN0xj5VbXiyAdIS9aO4";
  }

  // Sheet Names
  static get SHEET_NAMES() {
    return {
      GAMIFICATION: "Gamification",
      SOURCE: "NDailies",
      CONFIG: "Config",
      FORM_RESPONSES: "Respuestas de formulario 1",
      USER_ACHIEVEMENTS: "UserAchievements",
      GAME_CONFIG: "GameConfig",
      HELP: "📖 Ayuda"
    };
  }

  // Form Field Names
  static get FORM_FIELDS() {
    return {
      NAME: "IDENTIFICATE🤬",
      TIMESTAMP: "Marca temporal",
      YESTERDAY: "¿Qué hiciste ayer? (tareas finalizadas o avances)",
      TODAY: "¿Qué vas a hacer hoy? (tareas o trabajo planeado)",
      IMPEDIMENTS: "¿Tienes algún impedimento? (detalle lo que te está deteniendo, si hay algo)"
    };
  }

  // Badge Milestones
  static get BADGE_MILESTONES() {
    return [
      { days: 3, emoji: "🔥" },
      { days: 5, emoji: "🏅" },
      { days: 7, emoji: "🌟" },
      { days: 14, emoji: "🚀" },
      { days: 30, emoji: "🏆" }
    ];
  }

  // Cache Configuration
  static get CACHE_CONFIG() {
    return {
      ACHIEVEMENT_CACHE_DURATION: 30000, // 30 seconds
      BATCH_PROCESSING_DELAY: 1000, // 1 second
      NOTIFICATION_DELAY: 500 // 0.5 seconds
    };
  }

  // Time Configuration
  static get TIME_CONFIG() {
    return {
      HOURS_FOR_RECENT_DAILY: 24,
      SHIELD_CHECK_HOUR: 8,
      TIMEZONE: 'Europe/Madrid'
    };
  }

  // Shield System Configuration
  static get SHIELD_CONFIG() {
    return {
      FIRST_SHIELD_STREAK_REQUIRED: 7,
      REGENERATION_STREAK_REQUIRED: 2,
      MAX_SHIELDS_PER_USER: 1
    };
  }

  // Default Game Configuration
  static get DEFAULT_GAME_CONFIG() {
    return [
      ["PuntoBase", 1, true, "Punto por completar daily"],
      ["BonusLucky", 1, true, "Bonus en Lucky Day"],
      ["BonusWord", 1, true, "Bonus por usar palabra del día"],
      ["MultiplierWeekend", 1.5, false, "Multiplicador fin de semana"],
      ["BonusEarlyBird", 2, true, "Bonus por daily antes 9am"],
      ["BonusWeekendWarrior", 3, true, "Bonus por daily en fin de semana"],
      ["BonusDetailed", 1, true, "Bonus por daily detallado (+200 chars)"],
      ["BonusProjectWork", 3, true, "Bonus por mencionar trabajo del proyecto"],
      ["MultiplierProjectFocus", 1.5, true, "Multiplicador si daily es 80% sobre proyecto"]
    ];
  }

  // Project Keywords for Detection
  static get PROJECT_KEYWORDS() {
    return [
      // Development
      'proyecto', 'feature', 'desarrollo', 'implementar', 'implementé', 'código', 'programar', 'programé',
      'build', 'crear', 'creé', 'funcionalidad', 'component', 'módulo', 'clase', 'función',

      // Problem solving
      'solucion', 'arregl', 'fix', 'resolv', 'complet', 'termin', 'finaliz', 'acabé', 'acabar',
      'reparé', 'reparar', 'bug', 'error', 'problema', 'fallo', 'issue', 'defecto',

      // Testing and quality
      'test', 'testing', 'prueba', 'probar', 'probé', 'verificar', 'verificué', 'validar', 'validé',
      'revisar', 'revisé', 'review', 'pull request', 'pr', 'merge', 'aprobar', 'aprobé',

      // Documentation and planning
      'documentar', 'documenté', 'documentación', 'readme', 'wiki', 'comentar', 'comenté',
      'planificar', 'planifiqué', 'diseñar', 'diseñé', 'arquitectura', 'estructura',

      // Deploy and optimization
      'deploy', 'desplegar', 'desplegué', 'lanzar', 'lancé', 'publicar', 'publiqué', 'release',
      'optimizar', 'optimicé', 'mejorar', 'mejoré', 'refactor', 'refactorizar', 'performance'
    ];
  }

  // Tech Terms for Innovation Achievement
  static get TECH_TERMS() {
    return ['api', 'database', 'frontend', 'backend', 'deploy', 'git', 'commit', 'merge', 
            'pull request', 'bug', 'feature', 'refactor', 'testing', 'docker', 'kubernetes', 
            'microservice', 'framework', 'library', 'algorithm', 'optimization'];
  }

  // Puchaina Terms for Special Achievement
  static get PUCHAINA_TERMS() {
    return ['puchaina','gog','degue','pete','gogog','potaxie','fife','puchaina','jiafei',
            'tilin','tilinx','gog','gogog','pusei','amorch','y la queso','y punch','npc',
            'yassificar','woke','delulu','zutrin','pelgan','glibex', 'y la que'];
  }

  // Collaborative Terms
  static get COLLABORATIVE_TERMS() {
    return /\b(con|junto|equipo|compañer|colabor|ayud|revis|coordin)\b/i;
  }

  // Eclipse Dates for Legendary Achievement
  static get ECLIPSE_DATES() {
    return ["2025-03-29", "2025-09-21", "2026-02-17", "2026-08-12"];
  }

  // Fibonacci Numbers for Achievement
  static get FIBONACCI_NUMBERS() {
    return [21, 34, 55, 89, 144, 233, 377, 610, 987];
  }

  // Colors for Achievement
  static get COLORS() {
    return ['rojo', 'azul', 'verde', 'amarillo', 'negro', 'blanco', 'rosa', 
            'morado', 'naranja', 'gris', 'marrón', 'violeta'];
  }

  // Weather Terms for Achievement
  static get WEATHER_TERMS() {
    return ['lluvia', 'sol', 'nieve', 'viento', 'tormenta', 'nube', 'niebla', 
            'granizo', 'rayo', 'trueno'];
  }

  // Animals for Achievement
  static get ANIMALS() {
    return ['perro', 'gato', 'león', 'tigre', 'elefante', 'mono', 'pájaro', 'pez', 
            'serpiente', 'oso', 'lobo', 'zorro', 'conejo', 'ratón', 'caballo', 'vaca', 
            'cerdo', 'oveja'];
  }

  // Technologies for Achievement
  static get TECHNOLOGIES() {
    return ['javascript', 'python', 'java', 'react', 'node', 'css', 'html', 'sql', 
            'git', 'docker', 'unity', 'c#', 'c++', 'php', 'angular', 'vue'];
  }

  // Foods for Achievement
  static get FOODS() {
    return ['pizza', 'hamburguesa', 'pasta', 'arroz', 'pollo', 'pescado', 'ensalada', 
            'sopa', 'pan', 'queso', 'fruta', 'verdura', 'carne', 'huevo', 'leche', 'café', 'té'];
  }

  // Philosophical Terms for Achievement
  static get PHILOSOPHICAL_TERMS() {
    return ['existencia', 'consciencia', 'realidad', 'infinito'];
  }

  // Column Mappings for Gamification Sheet
  static get GAMIFICATION_COLUMNS() {
    return {
      NAME: 1,
      DEPARTMENT: 2,
      TOTAL: 3,
      POINTS: 4,
      STREAK: 5,
      MAX: 6,
      DATE: 7,
      BADGE: 8,
      DAYS_WITHOUT: 9,
      ACHIEVEMENTS: 10,
      SHIELD_AVAILABLE: 11,
      SHIELD_LOCK: 12,
      SHIELD_LAST_USED: 13
    };
  }
}
