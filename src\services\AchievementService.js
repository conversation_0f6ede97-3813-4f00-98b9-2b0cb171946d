/**
 * Achievement Service
 * Following Single Responsibility Principle - only handles achievement logic
 * Following Dependency Inversion Principle - depends on abstractions, not concretions
 */

class AchievementService extends IAchievementService {
  constructor(achievementRepository, userRepository) {
    super();
    this.achievementRepository = achievementRepository;
    this.userRepository = userRepository;
    this.achievements = this.loadAllAchievements();
  }

  /**
   * Load all achievements from configuration
   * @returns {Array<Achievement>}
   */
  loadAllAchievements() {
    const allConfigs = [
      ...AchievementConfig.UNIQUE_ACHIEVEMENTS,
      ...ProjectAchievementConfig.PROJECT_ACHIEVEMENTS,
      ...SpecialAchievementConfig.SPECIAL_ACHIEVEMENTS,
      ...LegendaryAchievementConfig.LEGENDARY_ACHIEVEMENTS
    ];

    return allConfigs.map(config => Achievement.fromConfig(config));
  }

  /**
   * Evaluate achievements for a daily entry
   * @param {Daily} daily - Daily object
   * @param {User} user - User object
   * @returns {Array<Achievement>}
   */
  evaluateAchievements(daily, user) {
    const earnedAchievements = [];
    const dailyData = daily.toLegacyFormat(); // For backward compatibility
    const userStats = this.buildUserStats(user, daily);

    for (const achievement of this.achievements) {
      // Skip if user already has this achievement
      if (this.hasUserAchievement(user.name, achievement.id)) {
        continue;
      }

      try {
        if (achievement.evaluate(dailyData, userStats)) {
          earnedAchievements.push(achievement);
        }
      } catch (error) {
        console.log(`❌ Error evaluating achievement ${achievement.id}: ${error.message}`);
      }
    }

    return earnedAchievements;
  }

  /**
   * Grant achievement to user
   * @param {string} userName - User name
   * @param {string} achievementId - Achievement ID
   * @returns {boolean}
   */
  grantAchievement(userName, achievementId) {
    const achievement = this.achievements.find(ach => ach.id === achievementId);
    if (!achievement) {
      console.log(`❌ Achievement not found: ${achievementId}`);
      return false;
    }

    return this.achievementRepository.grantAchievement(userName, achievement);
  }

  /**
   * Grant multiple achievements to user
   * @param {string} userName - User name
   * @param {Array<Achievement>} achievements - Achievements to grant
   * @returns {boolean}
   */
  grantAchievements(userName, achievements) {
    let allSuccess = true;
    
    for (const achievement of achievements) {
      const success = this.achievementRepository.grantAchievement(userName, achievement);
      if (!success) {
        allSuccess = false;
        console.log(`❌ Failed to grant achievement ${achievement.id} to ${userName}`);
      }
    }
    
    return allSuccess;
  }

  /**
   * Check if user has specific achievement
   * @param {string} userName - User name
   * @param {string} achievementId - Achievement ID
   * @returns {boolean}
   */
  hasUserAchievement(userName, achievementId) {
    return this.achievementRepository.hasUserAchievement(userName, achievementId);
  }

  /**
   * Get all achievements for a user
   * @param {string} userName - User name
   * @returns {Array}
   */
  getUserAchievements(userName) {
    return this.achievementRepository.getUserAchievements(userName);
  }

  /**
   * Get pending notifications for a user
   * @param {string} userName - User name
   * @returns {Array}
   */
  getPendingNotifications(userName) {
    return this.achievementRepository.getPendingNotifications(userName);
  }

  /**
   * Mark achievements as notified
   * @param {string} userName - User name
   * @param {Array} achievementIds - Array of achievement IDs
   * @returns {boolean}
   */
  markAsNotified(userName, achievementIds) {
    return this.achievementRepository.markAsNotified(userName, achievementIds);
  }

  /**
   * Build user statistics for achievement evaluation
   * @param {User} user - User object
   * @param {Daily} daily - Current daily
   * @returns {Object}
   */
  buildUserStats(user, daily) {
    // Get additional stats from daily repository if needed
    const baseStats = {
      totalDailies: user.totalDailies,
      currentStreak: user.currentStreak,
      maxStreak: user.maxStreak,
      points: user.points,
      departmentRank: 1, // Will be calculated by user service
      departmentAverage: 0, // Will be calculated by user service
      daysOfWeekCompleted: 0,
      luckyDaysHit: 0,
      wordOfDayUsed: 0,
      sameHourStreak: 0,
      perfectWeeks: 0,
      monthlyLongestDaily: false
    };

    return baseStats;
  }

  /**
   * Get achievement by ID
   * @param {string} achievementId - Achievement ID
   * @returns {Achievement|null}
   */
  getAchievementById(achievementId) {
    return this.achievements.find(ach => ach.id === achievementId) || null;
  }

  /**
   * Get achievements by category
   * @param {string} category - Achievement category
   * @returns {Array<Achievement>}
   */
  getAchievementsByCategory(category) {
    return this.achievements.filter(ach => ach.category === category);
  }

  /**
   * Get legendary achievements
   * @returns {Array<Achievement>}
   */
  getLegendaryAchievements() {
    return this.achievements.filter(ach => ach.isLegendary());
  }

  /**
   * Calculate total points from achievements
   * @param {Array} userAchievements - User achievements
   * @returns {number}
   */
  calculateAchievementPoints(userAchievements) {
    let totalPoints = 0;
    
    for (const userAch of userAchievements) {
      const achievement = this.getAchievementById(userAch.achievementId);
      if (achievement) {
        totalPoints += achievement.points;
      }
    }
    
    return totalPoints;
  }

  /**
   * Get achievement statistics
   * @returns {Object}
   */
  getAchievementStatistics() {
    return this.achievementRepository.getAchievementStats();
  }

  /**
   * Get rarest achievements
   * @param {number} limit - Number of achievements to return
   * @returns {Array}
   */
  getRarestAchievements(limit = 5) {
    return this.achievementRepository.getRarestAchievements(limit);
  }

  /**
   * Get most popular achievements
   * @param {number} limit - Number of achievements to return
   * @returns {Array}
   */
  getMostPopularAchievements(limit = 5) {
    return this.achievementRepository.getMostPopularAchievements(limit);
  }
}
