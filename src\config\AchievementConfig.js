/**
 * Achievement Configuration
 * Defines all unique achievements in the system
 * Following Single Responsibility Principle - only manages achievement definitions
 */

class AchievementConfig {
  static get UNIQUE_ACHIEVEMENTS() {
    return [
      // === INITIAL ACHIEVEMENTS ===
      {
        id: "first_daily",
        name: "Primer Paso",
        emoji: "🎯",
        points: 5,
        description: "Completar tu primera daily",
        category: "Inicio",
        condition: (_, userStats) => userStats.totalDailies === 1
      },

      // === VOLUME ACHIEVEMENTS ===
      {
        id: "sharpshooter",
        name: "<PERSON><PERSON><PERSON><PERSON>",
        emoji: "🎯",
        points: 10,
        description: "Completar 10 dailies",
        category: "Volumen",
        condition: (_, userStats) => userStats.totalDailies === 10
      },
      {
        id: "expert_archer",
        name: "<PERSON><PERSON><PERSON> Ex<PERSON>o",
        emoji: "🏹",
        points: 20,
        description: "Completar 25 dailies",
        category: "Volumen",
        condition: (_, userStats) => userStats.totalDailies === 25
      },
      {
        id: "veteran",
        name: "<PERSON><PERSON><PERSON>",
        emoji: "🎖️",
        points: 35,
        description: "Completar 50 dailies",
        category: "Volumen",
        condition: (_, userStats) => userStats.totalDailies === 50
      },
      {
        id: "legend",
        name: "Leyenda",
        emoji: "🏆",
        points: 50,
        description: "Completar 100 dailies",
        category: "Volumen",
        condition: (_, userStats) => userStats.totalDailies === 100
      },

      // === STREAK ACHIEVEMENTS ===
      {
        id: "on_fire",
        name: "En Llamas",
        emoji: "🔥",
        points: 15,
        description: "Racha de 5 días consecutivos",
        category: "Racha",
        condition: (_, userStats) => userStats.currentStreak === 5
      },
      {
        id: "supernova",
        name: "Supernova",
        emoji: "⭐",
        points: 25,
        description: "Racha de 10 días consecutivos",
        category: "Racha",
        condition: (_, userStats) => userStats.currentStreak === 10
      },
      {
        id: "unstoppable",
        name: "Imparable",
        emoji: "🚀",
        points: 40,
        description: "Racha de 15 días consecutivos",
        category: "Racha",
        condition: (_, userStats) => userStats.currentStreak === 15
      },
      {
        id: "consistency_king",
        name: "Rey de la Consistencia",
        emoji: "👑",
        points: 75,
        description: "Racha de 30 días consecutivos",
        category: "Racha",
        condition: (_, userStats) => userStats.currentStreak === 30
      },

      // === TIME-BASED ACHIEVEMENTS ===
      {
        id: "early_bird",
        name: "Madrugador",
        emoji: "🌅",
        points: 10,
        description: "Completar daily antes de las 9:00 AM",
        category: "Horario",
        condition: (data) => data.timestamp.getHours() < 9
      },
      {
        id: "night_owl",
        name: "Búho Nocturno",
        emoji: "🦉",
        points: 10,
        description: "Completar daily después de las 22:00 PM",
        category: "Horario",
        condition: (data) => data.timestamp.getHours() >= 22
      },
      {
        id: "weekend_warrior",
        name: "Guerrero de Fin de Semana",
        emoji: "⚔️",
        points: 15,
        description: "Completar daily en fin de semana",
        category: "Horario",
        condition: (data) => {
          const day = data.timestamp.getDay();
          return day === 0 || day === 6;
        }
      },

      // === CONTENT ACHIEVEMENTS ===
      {
        id: "writer",
        name: "Escritor",
        emoji: "📚",
        points: 10,
        description: "Daily con más de 300 caracteres",
        category: "Contenido",
        condition: (data) => (data.ayer + " " + data.hoy + " " + data.imped).length > 300
      },
      {
        id: "creative",
        name: "Creativo",
        emoji: "🎨",
        points: 5,
        description: "Usar emojis en tu daily",
        category: "Contenido",
        condition: (data) => {
          const text = data.ayer + " " + data.hoy + " " + data.imped;
          return /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(text);
        }
      },
      {
        id: "collaborative",
        name: "Colaborativo",
        emoji: "🔗",
        points: 8,
        description: "Mencionar a compañeros en tu daily",
        category: "Contenido",
        condition: (data) => {
          const text = data.ayer + " " + data.hoy + " " + data.imped;

          // 1. Search for @name patterns
          if (/@\w+/.test(text)) return true;

          // 2. Search for collaborative generic words
          if (/\b(con|junto|equipo|compañer|colabor|ayud|revis|coordin)\b/i.test(text)) return true;

          // 3. Search for specific participant names
          const participants = getAllParticipants();
          const currentUser = data.nombre;

          for (const participant of participants) {
            if (participant !== currentUser) {
              const firstName = participant.split(' ')[0];
              const namePattern = new RegExp(`\\b${participant}\\b|\\b${firstName}\\b`, 'i');
              if (namePattern.test(text)) {
                return true;
              }
            }
          }

          return false;
        }
      },
      {
        id: "innovator",
        name: "Innovador",
        emoji: "💡",
        points: 8,
        description: "Usar términos técnicos en tu daily",
        category: "Contenido",
        condition: (data) => {
          const text = (data.ayer + " " + data.hoy + " " + data.imped).toLowerCase();
          const techTerms = AppConfig.TECH_TERMS;
          return techTerms.some(term => text.includes(term));
        }
      }
    ];
  }

  // Get all achievements (this will be extended in separate files)
  static getAllAchievements() {
    return [
      ...this.UNIQUE_ACHIEVEMENTS,
      ...ProjectAchievementConfig.PROJECT_ACHIEVEMENTS,
      ...SpecialAchievementConfig.SPECIAL_ACHIEVEMENTS,
      ...LegendaryAchievementConfig.LEGENDARY_ACHIEVEMENTS
    ];
  }
}
