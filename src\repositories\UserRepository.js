/**
 * User Repository
 * Following Single Responsibility Principle - handles user data persistence
 */

class UserRepository extends BaseRepository {
  constructor() {
    const headers = [
      "Nombre", "Departamento", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", 
      "<PERSON><PERSON>", "<PERSON>ge", "Días sin", "Logros", "Escudo", "Escudo Lock", "Escudo Usado"
    ];
    super(AppConfig.SHEET_NAMES.GAMIFICATION, headers);
  }

  /**
   * Get user by name
   * @param {string} userName - User name
   * @returns {User|null}
   */
  getByName(userName) {
    const row = this.getFirst(row => row[0] === userName);
    return row ? User.fromSheetRow(row) : null;
  }

  /**
   * Get all users
   * @returns {Array<User>}
   */
  getAllUsers() {
    const rows = this.getAll();
    return rows.map(row => User.fromSheetRow(row));
  }

  /**
   * Get users by department
   * @param {string} department - Department name
   * @returns {Array<User>}
   */
  getByDepartment(department) {
    const rows = this.getWhere(row => row[1] === department);
    return rows.map(row => User.fromSheetRow(row));
  }

  /**
   * Create or update user
   * @param {User} user - User object
   * @returns {boolean}
   */
  saveUser(user) {
    const existingRowIndex = this.findRowIndex(row => row[0] === user.name);
    
    if (existingRowIndex > 0) {
      // Update existing user
      return this.updateByIndex(existingRowIndex, user.toSheetRow());
    } else {
      // Create new user
      return this.create(user.toSheetRow());
    }
  }

  /**
   * Update user points
   * @param {string} userName - User name
   * @param {number} points - New points value
   * @returns {boolean}
   */
  updateUserPoints(userName, points) {
    const rowIndex = this.findRowIndex(row => row[0] === userName);
    if (rowIndex > 0) {
      return this.updateCell(rowIndex, AppConfig.GAMIFICATION_COLUMNS.POINTS, points);
    }
    return false;
  }

  /**
   * Update user streak
   * @param {string} userName - User name
   * @param {number} currentStreak - Current streak
   * @param {number} maxStreak - Maximum streak
   * @returns {boolean}
   */
  updateUserStreak(userName, currentStreak, maxStreak) {
    const rowIndex = this.findRowIndex(row => row[0] === userName);
    if (rowIndex > 0) {
      const sheet = this.getSheet();
      sheet.getRange(rowIndex, AppConfig.GAMIFICATION_COLUMNS.STREAK, 1, 2)
           .setValues([[currentStreak, maxStreak]]);
      return true;
    }
    return false;
  }

  /**
   * Update user shield status
   * @param {string} userName - User name
   * @param {boolean} available - Shield availability
   * @param {boolean} lock - Shield lock status
   * @param {Date|null} lastUsed - Last used date
   * @returns {boolean}
   */
  updateUserShield(userName, available, lock = null, lastUsed = null) {
    const rowIndex = this.findRowIndex(row => row[0] === userName);
    if (rowIndex > 0) {
      const sheet = this.getSheet();
      const values = [available];
      
      if (lock !== null) values.push(lock);
      if (lastUsed !== null) values.push(lastUsed);
      
      sheet.getRange(rowIndex, AppConfig.GAMIFICATION_COLUMNS.SHIELD_AVAILABLE, 1, values.length)
           .setValues([values]);
      return true;
    }
    return false;
  }

  /**
   * Get top users by points
   * @param {number} limit - Number of top users to return
   * @returns {Array<User>}
   */
  getTopUsersByPoints(limit = 10) {
    const users = this.getAllUsers();
    return users
      .sort((a, b) => b.points - a.points)
      .slice(0, limit);
  }

  /**
   * Get top users by streak
   * @param {number} limit - Number of top users to return
   * @returns {Array<User>}
   */
  getTopUsersByStreak(limit = 10) {
    const users = this.getAllUsers();
    return users
      .sort((a, b) => b.currentStreak - a.currentStreak)
      .slice(0, limit);
  }

  /**
   * Get department statistics
   * @param {string} department - Department name
   * @returns {Object}
   */
  getDepartmentStats(department) {
    const departmentUsers = this.getByDepartment(department);
    
    if (departmentUsers.length === 0) {
      return { average: 0, total: 0, userCount: 0 };
    }

    const totalDailies = departmentUsers.reduce((sum, user) => sum + user.totalDailies, 0);
    const average = totalDailies / departmentUsers.length;

    return {
      average: Math.round(average * 100) / 100,
      total: totalDailies,
      userCount: departmentUsers.length
    };
  }

  /**
   * Get user rank in department
   * @param {string} userName - User name
   * @param {string} department - Department name
   * @returns {number}
   */
  getUserDepartmentRank(userName, department) {
    const departmentUsers = this.getByDepartment(department);
    departmentUsers.sort((a, b) => b.totalDailies - a.totalDailies);
    
    const userIndex = departmentUsers.findIndex(user => user.name === userName);
    return userIndex >= 0 ? userIndex + 1 : -1;
  }

  // Implementation of IRepository interface methods
  getById(id) {
    return this.getByName(id);
  }

  update(id, data) {
    return this.saveUser(data);
  }

  delete(id) {
    const rowIndex = this.findRowIndex(row => row[0] === id);
    return rowIndex > 0 ? this.deleteByIndex(rowIndex) : false;
  }
}
