/**
 * Google Apps Script Form Triggers
 * Following Single Responsibility Principle - only handles form submission triggers
 */

/**
 * Main form submission handler
 * This function is called by Google Apps Script when a form is submitted
 * @param {Object} e - Form submission event
 */
function onFormSubmit(e) {
  try {
    console.log('📝 Form submission received');
    
    // Get dependency container
    const container = getContainer();
    const dailyController = container.getDailyController();
    
    // Process form submission
    const result = dailyController.processFormSubmission(e);
    
    if (result.success) {
      console.log(`✅ Daily processed successfully for ${result.user.name}`);
      console.log(`📊 Points earned: ${result.pointsEarned}`);
      console.log(`🏆 Achievements earned: ${result.achievementsEarned.length}`);
      console.log(`🔥 Current streak: ${result.user.currentStreak}`);
    } else {
      console.log(`❌ Failed to process daily: ${result.errors.join(', ')}`);
    }
    
    return result;
  } catch (error) {
    console.log(`❌ Critical error in onFormSubmit: ${error.message}`);
    console.log(`Stack trace: ${error.stack}`);
    
    // Try to send error notification
    try {
      const container = getContainer();
      const discordService = container.getDiscordService();
      discordService.sendMessage(`🚨 **Error crítico en el sistema**\n\`\`\`${error.message}\`\`\``);
    } catch (notificationError) {
      console.log(`❌ Failed to send error notification: ${notificationError.message}`);
    }
    
    return { success: false, errors: [error.message] };
  }
}

/**
 * Test form submission with sample data
 * Useful for testing the system without actual form submissions
 */
function testFormSubmission() {
  const sampleFormData = {
    namedValues: {
      [AppConfig.FORM_FIELDS.TIMESTAMP]: [new Date().toISOString()],
      [AppConfig.FORM_FIELDS.NAME]: ["Test User"],
      [AppConfig.FORM_FIELDS.YESTERDAY]: ["Trabajé en el proyecto de refactoring aplicando principios SOLID"],
      [AppConfig.FORM_FIELDS.TODAY]: ["Voy a continuar con la implementación de los servicios y controladores"],
      [AppConfig.FORM_FIELDS.IMPEDIMENTS]: ["Ningún impedimento por ahora"]
    }
  };
  
  console.log('🧪 Testing form submission with sample data');
  return onFormSubmit(sampleFormData);
}

/**
 * Validate form trigger setup
 * Checks if the form trigger is properly configured
 */
function validateFormTrigger() {
  try {
    const triggers = ScriptApp.getProjectTriggers();
    const formTriggers = triggers.filter(trigger => 
      trigger.getEventType() === ScriptApp.EventType.ON_FORM_SUBMIT
    );
    
    console.log(`📋 Found ${formTriggers.length} form trigger(s)`);
    
    formTriggers.forEach((trigger, index) => {
      console.log(`Trigger ${index + 1}:`);
      console.log(`  - Handler: ${trigger.getHandlerFunction()}`);
      console.log(`  - Source: ${trigger.getTriggerSource().getName()}`);
    });
    
    if (formTriggers.length === 0) {
      console.log('⚠️ No form triggers found. Please set up a form trigger for onFormSubmit function.');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Error validating form trigger: ${error.message}`);
    return false;
  }
}

/**
 * Setup form trigger programmatically
 * Creates a form submission trigger for the active form
 */
function setupFormTrigger() {
  try {
    // Get the active spreadsheet
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    
    // Get the form associated with this spreadsheet
    const form = FormApp.openByUrl(ss.getFormUrl());
    
    // Delete existing triggers
    const existingTriggers = ScriptApp.getProjectTriggers();
    existingTriggers.forEach(trigger => {
      if (trigger.getEventType() === ScriptApp.EventType.ON_FORM_SUBMIT) {
        ScriptApp.deleteTrigger(trigger);
      }
    });
    
    // Create new trigger
    ScriptApp.newTrigger('onFormSubmit')
             .onFormSubmit()
             .create();
    
    console.log('✅ Form trigger setup successfully');
    return true;
  } catch (error) {
    console.log(`❌ Error setting up form trigger: ${error.message}`);
    return false;
  }
}

/**
 * Remove all form triggers
 * Useful for cleanup or reconfiguration
 */
function removeFormTriggers() {
  try {
    const triggers = ScriptApp.getProjectTriggers();
    const formTriggers = triggers.filter(trigger => 
      trigger.getEventType() === ScriptApp.EventType.ON_FORM_SUBMIT
    );
    
    formTriggers.forEach(trigger => {
      ScriptApp.deleteTrigger(trigger);
    });
    
    console.log(`✅ Removed ${formTriggers.length} form trigger(s)`);
    return true;
  } catch (error) {
    console.log(`❌ Error removing form triggers: ${error.message}`);
    return false;
  }
}
