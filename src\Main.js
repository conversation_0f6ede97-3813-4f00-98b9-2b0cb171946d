/**
 * Main Entry Point for Daily Gamification System
 * Following SOLID principles with proper dependency injection
 * Maintains backward compatibility with existing Google Apps Script triggers
 */

// ===== BACKWARD COMPATIBILITY FUNCTIONS =====
// These functions maintain the same interface as the original code.js

/**
 * Legacy function for getting or creating sheet
 * @param {string} name - Sheet name
 * @param {Array} header - Header row
 * @returns {GoogleAppsScript.Spreadsheet.Sheet}
 */
function getOrCreateSheet(name, header) {
  const baseRepo = new BaseRepository(name, header);
  return baseRepo.getSheet();
}

/**
 * Legacy function for getting all participants
 * @returns {Array<string>}
 */
function getAllParticipants() {
  const container = getContainer();
  const userService = container.getUserService();
  const users = userService.userRepository.getAllUsers();
  return users.map(user => user.name);
}

/**
 * Legacy function for getting user data
 * @param {string} userName - User name
 * @returns {Object|null}
 */
function getUserData(userName) {
  const container = getContainer();
  const userService = container.getUserService();
  const user = userService.userRepository.getByName(userName);
  
  if (!user) return null;
  
  // Convert to legacy format
  return {
    nombre: user.name,
    departamento: user.department,
    total: user.totalDailies,
    puntos: user.points,
    racha: user.currentStreak,
    max: user.maxStreak,
    fecha: user.lastDate,
    badge: user.badge,
    diasSin: user.daysWithoutDaily,
    logros: user.achievements,
    escudo: user.shieldAvailable,
    escudoLock: user.shieldLock,
    escudoUsado: user.shieldLastUsed
  };
}

/**
 * Legacy function for updating user data
 * @param {string} userName - User name
 * @param {Object} userData - User data
 * @returns {boolean}
 */
function updateUserData(userName, userData) {
  const container = getContainer();
  const userService = container.getUserService();
  
  const user = userService.userRepository.getByName(userName);
  if (!user) return false;
  
  // Update user properties from legacy format
  if (userData.puntos !== undefined) user.points = userData.puntos;
  if (userData.racha !== undefined) user.currentStreak = userData.racha;
  if (userData.max !== undefined) user.maxStreak = userData.max;
  if (userData.fecha !== undefined) user.lastDate = userData.fecha;
  if (userData.badge !== undefined) user.badge = userData.badge;
  if (userData.diasSin !== undefined) user.daysWithoutDaily = userData.diasSin;
  if (userData.logros !== undefined) user.achievements = userData.logros;
  if (userData.escudo !== undefined) user.shieldAvailable = userData.escudo;
  if (userData.escudoLock !== undefined) user.shieldLock = userData.escudoLock;
  if (userData.escudoUsado !== undefined) user.shieldLastUsed = userData.escudoUsado;
  
  return userService.userRepository.saveUser(user);
}

/**
 * Legacy function for getting game configuration
 * @returns {Object}
 */
function getGameConfig() {
  const container = getContainer();
  const gameConfigRepo = container.resolve('gameConfigRepository');
  const gameConfig = gameConfigRepo.getGameConfig();
  return gameConfig.toObject();
}

// ===== MAIN SYSTEM FUNCTIONS =====

/**
 * Initialize the gamification system
 * Sets up all dependencies and validates configuration
 */
function initializeSystem() {
  try {
    console.log('🚀 Initializing Daily Gamification System v2.0');
    
    // Get container and initialize dependencies
    const container = getContainer();
    
    // Validate system health
    const adminController = container.getAdminController();
    const health = adminController.getSystemHealth();
    
    if (health.status === 'healthy') {
      console.log('✅ System initialized successfully');
      console.log(`📊 Users: ${health.components.users.count}`);
      console.log(`🏆 Achievements: ${health.components.achievements.count}`);
      console.log(`⚙️ Game config: ${health.components.gameConfig.valid ? 'Valid' : 'Invalid'}`);
    } else {
      console.log(`❌ System initialization failed: ${health.error}`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Critical error during system initialization: ${error.message}`);
    return false;
  }
}

/**
 * Get system dashboard data
 * @returns {Object}
 */
function getSystemDashboard() {
  try {
    const container = getContainer();
    const adminController = container.getAdminController();
    const dailyController = container.getDailyController();
    
    const systemStats = adminController.getSystemStatistics();
    const recentActivity = dailyController.getRecentActivity(7);
    const topUsersByPoints = dailyController.getLeaderboard('points', 10);
    const topUsersByStreak = dailyController.getLeaderboard('streak', 10);
    
    return {
      systemStats: systemStats,
      recentActivity: recentActivity,
      leaderboards: {
        points: topUsersByPoints,
        streak: topUsersByStreak
      },
      lastUpdate: new Date()
    };
  } catch (error) {
    console.log(`❌ Error getting system dashboard: ${error.message}`);
    return null;
  }
}

/**
 * Get user dashboard data
 * @param {string} userName - User name
 * @returns {Object}
 */
function getUserDashboard(userName) {
  try {
    const container = getContainer();
    const dailyController = container.getDailyController();
    
    return dailyController.getUserDashboard(userName);
  } catch (error) {
    console.log(`❌ Error getting user dashboard for ${userName}: ${error.message}`);
    return null;
  }
}

/**
 * Manual system health check
 * @returns {Object}
 */
function checkSystemHealth() {
  try {
    const container = getContainer();
    const adminController = container.getAdminController();
    
    const health = adminController.getSystemHealth();
    
    console.log(`🏥 System Health Check:`);
    console.log(`Status: ${health.status}`);
    
    if (health.status === 'healthy') {
      Object.entries(health.components).forEach(([component, status]) => {
        console.log(`  ${component}: ${status.status} ${status.count ? `(${status.count})` : ''}`);
      });
    } else {
      console.log(`Error: ${health.error}`);
    }
    
    return health;
  } catch (error) {
    console.log(`❌ Error during health check: ${error.message}`);
    return { status: 'error', error: error.message };
  }
}

/**
 * Backup system data
 * @returns {Object}
 */
function backupSystemData() {
  try {
    const container = getContainer();
    const adminController = container.getAdminController();
    
    const backup = adminController.backupSystemData();
    
    if (backup.success) {
      console.log('✅ System backup created successfully');
    } else {
      console.log(`❌ Backup failed: ${backup.error}`);
    }
    
    return backup;
  } catch (error) {
    console.log(`❌ Error creating backup: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test Discord connection
 * @returns {Promise<boolean>}
 */
async function testDiscordConnection() {
  try {
    const container = getContainer();
    const discordService = container.getDiscordService();
    
    const success = await discordService.testConnection();
    
    if (success) {
      console.log('✅ Discord connection test successful');
    } else {
      console.log('❌ Discord connection test failed');
    }
    
    return success;
  } catch (error) {
    console.log(`❌ Error testing Discord connection: ${error.message}`);
    return false;
  }
}

/**
 * Setup complete system (triggers + initialization)
 * @returns {boolean}
 */
function setupCompleteSystem() {
  try {
    console.log('🔧 Setting up complete Daily Gamification System');
    
    // 1. Initialize system
    const initSuccess = initializeSystem();
    if (!initSuccess) {
      console.log('❌ System initialization failed');
      return false;
    }
    
    // 2. Setup form triggers
    const formTriggerSuccess = setupFormTrigger();
    if (!formTriggerSuccess) {
      console.log('⚠️ Form trigger setup failed, but continuing...');
    }
    
    // 3. Setup time triggers
    const timeTriggerSuccess = setupTimeTriggers();
    if (!timeTriggerSuccess) {
      console.log('⚠️ Time trigger setup failed, but continuing...');
    }
    
    // 4. Test Discord connection
    testDiscordConnection();
    
    console.log('✅ Complete system setup finished');
    return true;
  } catch (error) {
    console.log(`❌ Error during complete system setup: ${error.message}`);
    return false;
  }
}

// ===== SYSTEM INFORMATION =====

/**
 * Get system information
 * @returns {Object}
 */
function getSystemInfo() {
  return {
    name: "Daily Gamification System",
    version: "2.0.0-SOLID",
    architecture: "SOLID Principles",
    components: {
      models: ["User", "Daily", "Achievement", "GameConfig"],
      services: ["UserService", "AchievementService", "PointsService", "ShieldService", "DiscordService"],
      repositories: ["UserRepository", "AchievementRepository", "DailyRepository", "GameConfigRepository"],
      controllers: ["DailyController", "AdminController"],
      utils: ["DateUtils", "TextUtils", "ValidationUtils", "CacheUtils"]
    },
    principles: {
      "Single Responsibility": "Each class has one reason to change",
      "Open/Closed": "Open for extension, closed for modification",
      "Liskov Substitution": "Subtypes must be substitutable for their base types",
      "Interface Segregation": "Many client-specific interfaces are better than one general-purpose interface",
      "Dependency Inversion": "Depend on abstractions, not concretions"
    },
    lastUpdate: new Date()
  };
}
