/**
 * Achievement Domain Model
 * Following Single Responsibility Principle - represents achievement entity and its behavior
 */

class Achievement {
  constructor(data = {}) {
    this.id = data.id || '';
    this.name = data.name || '';
    this.emoji = data.emoji || '';
    this.points = data.points || 0;
    this.description = data.description || '';
    this.category = data.category || '';
    this.condition = data.condition || (() => false);
  }

  /**
   * Evaluate if achievement condition is met
   * @param {Object} dailyData - Daily data
   * @param {Object} userStats - User statistics
   * @returns {boolean}
   */
  evaluate(dailyData, userStats) {
    try {
      return this.condition(dailyData, userStats);
    } catch (error) {
      console.log(`❌ Error evaluating achievement ${this.id}: ${error.message}`);
      return false;
    }
  }

  /**
   * Get display text for achievement
   * @returns {string}
   */
  getDisplayText() {
    return `${this.emoji} **${this.name}** - ${this.description} (+${this.points} pts)`;
  }

  /**
   * Check if achievement is legendary (high points)
   * @returns {boolean}
   */
  isLegendary() {
    return this.points >= 50 || this.category === "Legendario";
  }

  /**
   * Check if achievement is project-related
   * @returns {boolean}
   */
  isProjectRelated() {
    return this.category === "Proyecto";
  }

  /**
   * Check if achievement is time-based
   * @returns {boolean}
   */
  isTimeBased() {
    return this.category === "Horario" || this.category === "Evento";
  }

  /**
   * Check if achievement is content-based
   * @returns {boolean}
   */
  isContentBased() {
    return this.category === "Contenido";
  }

  /**
   * Convert to sheet row format
   * @param {string} userName - User who earned the achievement
   * @param {Date} dateEarned - Date when achievement was earned
   * @param {boolean} notified - Whether user has been notified
   * @returns {Array}
   */
  toSheetRow(userName, dateEarned = new Date(), notified = false) {
    return [
      userName,
      this.id,
      dateEarned,
      this.points,
      this.category,
      notified
    ];
  }

  /**
   * Create Achievement from configuration
   * @param {Object} config - Achievement configuration
   * @returns {Achievement}
   */
  static fromConfig(config) {
    return new Achievement(config);
  }

  /**
   * Create UserAchievement from sheet row
   * @param {Array} row - Sheet row data
   * @returns {Object}
   */
  static userAchievementFromSheetRow(row) {
    return {
      userName: row[0] || '',
      achievementId: row[1] || '',
      dateEarned: row[2] || new Date(),
      points: row[3] || 0,
      category: row[4] || '',
      notified: row[5] || false
    };
  }

  /**
   * Validate achievement configuration
   * @returns {Object} Validation result
   */
  validate() {
    const errors = [];
    
    if (!this.id) {
      errors.push('Achievement ID is required');
    }
    
    if (!this.name) {
      errors.push('Achievement name is required');
    }
    
    if (!this.description) {
      errors.push('Achievement description is required');
    }
    
    if (typeof this.points !== 'number' || this.points < 0) {
      errors.push('Achievement points must be a positive number');
    }
    
    if (typeof this.condition !== 'function') {
      errors.push('Achievement condition must be a function');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}
