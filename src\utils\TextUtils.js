/**
 * Text Utilities
 * Following Single Responsibility Principle - only handles text operations
 */

class TextUtils {
  /**
   * Clean and normalize text
   * @param {string} text - Text to clean
   * @returns {string}
   */
  static cleanText(text) {
    if (!text || typeof text !== 'string') return '';
    
    return text.trim()
               .replace(/\s+/g, ' ')  // Replace multiple spaces with single space
               .replace(/\n+/g, '\n'); // Replace multiple newlines with single newline
  }

  /**
   * Count words in text
   * @param {string} text - Text to count
   * @returns {number}
   */
  static countWords(text) {
    if (!text || typeof text !== 'string') return 0;
    
    const cleanedText = this.cleanText(text);
    if (!cleanedText) return 0;
    
    return cleanedText.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Count characters in text
   * @param {string} text - Text to count
   * @returns {number}
   */
  static countCharacters(text) {
    if (!text || typeof text !== 'string') return 0;
    return text.length;
  }

  /**
   * Extract emojis from text
   * @param {string} text - Text to extract from
   * @returns {Array}
   */
  static extractEmojis(text) {
    if (!text || typeof text !== 'string') return [];
    
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
    return text.match(emojiRegex) || [];
  }

  /**
   * Extract unique emojis from text
   * @param {string} text - Text to extract from
   * @returns {Array}
   */
  static extractUniqueEmojis(text) {
    const emojis = this.extractEmojis(text);
    return [...new Set(emojis)];
  }

  /**
   * Extract numbers from text
   * @param {string} text - Text to extract from
   * @returns {Array}
   */
  static extractNumbers(text) {
    if (!text || typeof text !== 'string') return [];
    
    return text.match(/\b\d+\b/g) || [];
  }

  /**
   * Extract unique numbers from text
   * @param {string} text - Text to extract from
   * @returns {Array}
   */
  static extractUniqueNumbers(text) {
    const numbers = this.extractNumbers(text);
    return [...new Set(numbers)];
  }

  /**
   * Check if text contains any of the specified terms
   * @param {string} text - Text to search in
   * @param {Array} terms - Terms to search for
   * @param {boolean} caseSensitive - Whether search is case sensitive
   * @returns {boolean}
   */
  static containsAnyTerm(text, terms, caseSensitive = false) {
    if (!text || !terms || !Array.isArray(terms)) return false;
    
    const searchText = caseSensitive ? text : text.toLowerCase();
    
    return terms.some(term => {
      const searchTerm = caseSensitive ? term : term.toLowerCase();
      return searchText.includes(searchTerm);
    });
  }

  /**
   * Check if text contains all specified terms
   * @param {string} text - Text to search in
   * @param {Array} terms - Terms to search for
   * @param {boolean} caseSensitive - Whether search is case sensitive
   * @returns {boolean}
   */
  static containsAllTerms(text, terms, caseSensitive = false) {
    if (!text || !terms || !Array.isArray(terms)) return false;
    
    const searchText = caseSensitive ? text : text.toLowerCase();
    
    return terms.every(term => {
      const searchTerm = caseSensitive ? term : term.toLowerCase();
      return searchText.includes(searchTerm);
    });
  }

  /**
   * Count occurrences of terms in text
   * @param {string} text - Text to search in
   * @param {Array} terms - Terms to count
   * @param {boolean} caseSensitive - Whether search is case sensitive
   * @returns {Object}
   */
  static countTermOccurrences(text, terms, caseSensitive = false) {
    if (!text || !terms || !Array.isArray(terms)) return {};
    
    const searchText = caseSensitive ? text : text.toLowerCase();
    const counts = {};
    
    terms.forEach(term => {
      const searchTerm = caseSensitive ? term : term.toLowerCase();
      const regex = new RegExp(`\\b${this.escapeRegex(searchTerm)}\\b`, 'g');
      const matches = searchText.match(regex);
      counts[term] = matches ? matches.length : 0;
    });
    
    return counts;
  }

  /**
   * Escape special regex characters
   * @param {string} text - Text to escape
   * @returns {string}
   */
  static escapeRegex(text) {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Check if text matches pattern
   * @param {string} text - Text to check
   * @param {RegExp} pattern - Regular expression pattern
   * @returns {boolean}
   */
  static matchesPattern(text, pattern) {
    if (!text || !pattern) return false;
    return pattern.test(text);
  }

  /**
   * Extract mentions (@username) from text
   * @param {string} text - Text to extract from
   * @returns {Array}
   */
  static extractMentions(text) {
    if (!text || typeof text !== 'string') return [];
    
    const mentionRegex = /@(\w+)/g;
    const mentions = [];
    let match;
    
    while ((match = mentionRegex.exec(text)) !== null) {
      mentions.push(match[1]);
    }
    
    return [...new Set(mentions)]; // Return unique mentions
  }

  /**
   * Truncate text to specified length
   * @param {string} text - Text to truncate
   * @param {number} maxLength - Maximum length
   * @param {string} suffix - Suffix to add when truncated
   * @returns {string}
   */
  static truncate(text, maxLength = 100, suffix = '...') {
    if (!text || typeof text !== 'string') return '';
    
    if (text.length <= maxLength) return text;
    
    return text.substring(0, maxLength - suffix.length) + suffix;
  }

  /**
   * Capitalize first letter of each word
   * @param {string} text - Text to capitalize
   * @returns {string}
   */
  static capitalizeWords(text) {
    if (!text || typeof text !== 'string') return '';
    
    return text.replace(/\b\w/g, char => char.toUpperCase());
  }

  /**
   * Remove emojis from text
   * @param {string} text - Text to clean
   * @returns {string}
   */
  static removeEmojis(text) {
    if (!text || typeof text !== 'string') return '';
    
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
    return text.replace(emojiRegex, '').trim();
  }

  /**
   * Get text statistics
   * @param {string} text - Text to analyze
   * @returns {Object}
   */
  static getTextStatistics(text) {
    if (!text || typeof text !== 'string') {
      return {
        characters: 0,
        words: 0,
        sentences: 0,
        emojis: 0,
        uniqueEmojis: 0,
        numbers: 0,
        uniqueNumbers: 0
      };
    }

    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const emojis = this.extractEmojis(text);
    const uniqueEmojis = this.extractUniqueEmojis(text);
    const numbers = this.extractNumbers(text);
    const uniqueNumbers = this.extractUniqueNumbers(text);

    return {
      characters: this.countCharacters(text),
      words: this.countWords(text),
      sentences: sentences.length,
      emojis: emojis.length,
      uniqueEmojis: uniqueEmojis.length,
      numbers: numbers.length,
      uniqueNumbers: uniqueNumbers.length
    };
  }

  /**
   * Check if text is meaningful (not just spaces/punctuation)
   * @param {string} text - Text to check
   * @returns {boolean}
   */
  static isMeaningfulText(text) {
    if (!text || typeof text !== 'string') return false;
    
    const cleanedText = text.replace(/[^\w]/g, '');
    return cleanedText.length > 0;
  }
}
