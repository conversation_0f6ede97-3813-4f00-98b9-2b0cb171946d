/**
 * User Service
 * Following Single Responsibility Principle - only handles user-related business logic
 * Following Dependency Inversion Principle - depends on abstractions
 */

class UserService {
  constructor(userRepository, dailyRepository) {
    this.userRepository = userRepository;
    this.dailyRepository = dailyRepository;
  }

  /**
   * Get or create user
   * @param {string} userName - User name
   * @param {string} department - User department
   * @returns {User}
   */
  getOrCreateUser(userName, department = '') {
    let user = this.userRepository.getByName(userName);
    
    if (!user) {
      user = new User({
        name: userName,
        department: department,
        totalDailies: 0,
        points: 0,
        currentStreak: 0,
        maxStreak: 0,
        lastDate: null,
        badge: '',
        daysWithoutDaily: 0,
        achievements: '',
        shieldAvailable: false, // First shield must be earned
        shieldLock: false,
        shieldLastUsed: null
      });
      
      this.userRepository.saveUser(user);
    }
    
    return user;
  }

  /**
   * Process daily submission for user
   * @param {Daily} daily - Daily object
   * @returns {Object} Processing result
   */
  processDailySubmission(daily) {
    const user = this.getOrCreateUser(daily.userName);
    
    // Update streak
    const streakResult = user.updateStreak(daily.timestamp);
    
    // Increment total dailies
    user.incrementDailies();
    
    // Update badge
    user.badge = user.getCurrentBadge();
    
    // Update days without daily
    user.daysWithoutDaily = 0;
    
    // Save user
    this.userRepository.saveUser(user);
    
    return {
      user: user,
      streakResult: streakResult,
      isNewUser: user.totalDailies === 1
    };
  }

  /**
   * Add points to user
   * @param {string} userName - User name
   * @param {number} points - Points to add
   * @returns {boolean}
   */
  addPointsToUser(userName, points) {
    const user = this.userRepository.getByName(userName);
    if (!user) return false;
    
    user.addPoints(points);
    return this.userRepository.saveUser(user);
  }

  /**
   * Get user statistics with enhanced data
   * @param {string} userName - User name
   * @returns {Object}
   */
  getUserStatistics(userName) {
    const user = this.userRepository.getByName(userName);
    if (!user) return null;

    const dailyStats = this.dailyRepository.getUserStats(userName);
    const departmentStats = this.userRepository.getDepartmentStats(user.department);
    const departmentRank = this.userRepository.getUserDepartmentRank(userName, user.department);

    return {
      ...user,
      ...dailyStats,
      departmentAverage: departmentStats.average,
      departmentRank: departmentRank,
      departmentTotal: departmentStats.total
    };
  }

  /**
   * Get top users by points
   * @param {number} limit - Number of users to return
   * @returns {Array<User>}
   */
  getTopUsersByPoints(limit = 10) {
    return this.userRepository.getTopUsersByPoints(limit);
  }

  /**
   * Get top users by streak
   * @param {number} limit - Number of users to return
   * @returns {Array<User>}
   */
  getTopUsersByStreak(limit = 10) {
    return this.userRepository.getTopUsersByStreak(limit);
  }

  /**
   * Get users by department
   * @param {string} department - Department name
   * @returns {Array<User>}
   */
  getUsersByDepartment(department) {
    return this.userRepository.getByDepartment(department);
  }

  /**
   * Get all departments
   * @returns {Array<string>}
   */
  getAllDepartments() {
    const users = this.userRepository.getAllUsers();
    const departments = [...new Set(users.map(user => user.department).filter(dept => dept))];
    return departments.sort();
  }

  /**
   * Update user department
   * @param {string} userName - User name
   * @param {string} department - New department
   * @returns {boolean}
   */
  updateUserDepartment(userName, department) {
    const user = this.userRepository.getByName(userName);
    if (!user) return false;
    
    user.department = department;
    return this.userRepository.saveUser(user);
  }

  /**
   * Check users who need shield protection
   * @returns {Array<User>}
   */
  getUsersNeedingShieldCheck() {
    const users = this.userRepository.getAllUsers();
    const now = new Date();
    
    return users.filter(user => {
      if (!user.lastDate) return false;
      
      const lastDaily = user.lastDate instanceof Date ? user.lastDate : new Date(user.lastDate);
      const hoursSinceLastDaily = (now - lastDaily) / (1000 * 60 * 60);
      
      // Check if it's past the shield check hour and user hasn't submitted today
      return hoursSinceLastDaily > AppConfig.TIME_CONFIG.SHIELD_CHECK_HOUR && 
             user.currentStreak > 0 && 
             user.shieldAvailable;
    });
  }

  /**
   * Get users with broken streaks (for shield consumption)
   * @returns {Array<User>}
   */
  getUsersWithBrokenStreaks() {
    const users = this.userRepository.getAllUsers();
    const now = new Date();
    
    return users.filter(user => {
      if (!user.lastDate) return false;
      
      const lastDaily = user.lastDate instanceof Date ? user.lastDate : new Date(user.lastDate);
      const daysSinceLastDaily = Math.floor((now - lastDaily) / (1000 * 60 * 60 * 24));
      
      return daysSinceLastDaily > 1 && user.currentStreak > 0;
    });
  }

  /**
   * Reset user streak
   * @param {string} userName - User name
   * @returns {boolean}
   */
  resetUserStreak(userName) {
    const user = this.userRepository.getByName(userName);
    if (!user) return false;
    
    user.currentStreak = 0;
    user.badge = '';
    user.daysWithoutDaily = user.getDaysSinceLastDaily();
    
    return this.userRepository.saveUser(user);
  }

  /**
   * Get user leaderboard
   * @param {string} sortBy - Sort criteria ('points', 'streak', 'dailies')
   * @param {number} limit - Number of users to return
   * @returns {Array<Object>}
   */
  getLeaderboard(sortBy = 'points', limit = 10) {
    const users = this.userRepository.getAllUsers();
    
    let sortedUsers;
    switch (sortBy) {
      case 'streak':
        sortedUsers = users.sort((a, b) => b.currentStreak - a.currentStreak);
        break;
      case 'dailies':
        sortedUsers = users.sort((a, b) => b.totalDailies - a.totalDailies);
        break;
      case 'points':
      default:
        sortedUsers = users.sort((a, b) => b.points - a.points);
        break;
    }
    
    return sortedUsers.slice(0, limit).map((user, index) => ({
      rank: index + 1,
      name: user.name,
      department: user.department,
      value: sortBy === 'streak' ? user.currentStreak : 
             sortBy === 'dailies' ? user.totalDailies : user.points,
      badge: user.badge
    }));
  }

  /**
   * Get department leaderboard
   * @param {string} department - Department name
   * @param {string} sortBy - Sort criteria
   * @param {number} limit - Number of users to return
   * @returns {Array<Object>}
   */
  getDepartmentLeaderboard(department, sortBy = 'points', limit = 10) {
    const departmentUsers = this.userRepository.getByDepartment(department);
    
    let sortedUsers;
    switch (sortBy) {
      case 'streak':
        sortedUsers = departmentUsers.sort((a, b) => b.currentStreak - a.currentStreak);
        break;
      case 'dailies':
        sortedUsers = departmentUsers.sort((a, b) => b.totalDailies - a.totalDailies);
        break;
      case 'points':
      default:
        sortedUsers = departmentUsers.sort((a, b) => b.points - a.points);
        break;
    }
    
    return sortedUsers.slice(0, limit).map((user, index) => ({
      rank: index + 1,
      name: user.name,
      value: sortBy === 'streak' ? user.currentStreak : 
             sortBy === 'dailies' ? user.totalDailies : user.points,
      badge: user.badge
    }));
  }

  /**
   * Get overall statistics
   * @returns {Object}
   */
  getOverallStatistics() {
    const users = this.userRepository.getAllUsers();
    
    if (users.length === 0) {
      return {
        totalUsers: 0,
        totalDailies: 0,
        averagePoints: 0,
        averageStreak: 0,
        longestStreak: 0,
        totalPoints: 0
      };
    }

    const totalDailies = users.reduce((sum, user) => sum + user.totalDailies, 0);
    const totalPoints = users.reduce((sum, user) => sum + user.points, 0);
    const totalStreak = users.reduce((sum, user) => sum + user.currentStreak, 0);
    const longestStreak = Math.max(...users.map(user => user.maxStreak));

    return {
      totalUsers: users.length,
      totalDailies: totalDailies,
      averagePoints: Math.round(totalPoints / users.length),
      averageStreak: Math.round((totalStreak / users.length) * 100) / 100,
      longestStreak: longestStreak,
      totalPoints: totalPoints
    };
  }
}
